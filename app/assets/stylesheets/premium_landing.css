/* Premium Landing Page Design System */

/* Import Google Fonts for Premium Typography */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');

:root {
  /* Premium Color Palette */
  --premium-primary: #6366f1;
  --premium-primary-light: #818cf8;
  --premium-primary-dark: #4f46e5;
  --premium-secondary: #06b6d4;
  --premium-accent: #f59e0b;
  --premium-success: #10b981;
  --premium-warning: #f59e0b;
  --premium-error: #ef4444;
  
  /* Premium Neutrals */
  --premium-white: #ffffff;
  --premium-gray-50: #fafafa;
  --premium-gray-100: #f4f4f5;
  --premium-gray-200: #e4e4e7;
  --premium-gray-300: #d4d4d8;
  --premium-gray-400: #a1a1aa;
  --premium-gray-500: #71717a;
  --premium-gray-600: #52525b;
  --premium-gray-700: #3f3f46;
  --premium-gray-800: #27272a;
  --premium-gray-900: #18181b;
  
  /* Premium Gradients */
  --premium-gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --premium-gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --premium-gradient-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --premium-gradient-warm: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  --premium-gradient-cool: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  
  /* Premium Typography */
  --premium-font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --premium-font-display: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  
  /* Premium Spacing */
  --premium-space-1: 0.25rem;
  --premium-space-2: 0.5rem;
  --premium-space-3: 0.75rem;
  --premium-space-4: 1rem;
  --premium-space-5: 1.25rem;
  --premium-space-6: 1.5rem;
  --premium-space-8: 2rem;
  --premium-space-10: 2.5rem;
  --premium-space-12: 3rem;
  --premium-space-16: 4rem;
  --premium-space-20: 5rem;
  --premium-space-24: 6rem;
  --premium-space-32: 8rem;
  
  /* Premium Shadows */
  --premium-shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --premium-shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --premium-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --premium-shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --premium-shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --premium-shadow-inner: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);
  
  /* Premium Border Radius */
  --premium-radius-sm: 0.375rem;
  --premium-radius-md: 0.5rem;
  --premium-radius-lg: 0.75rem;
  --premium-radius-xl: 1rem;
  --premium-radius-2xl: 1.5rem;
  --premium-radius-3xl: 2rem;
  --premium-radius-full: 9999px;
  
  /* Premium Transitions */
  --premium-transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --premium-transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --premium-transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Premium Base Styles */
.premium-landing {
  font-family: var(--premium-font-family);
  line-height: 1.6;
  color: var(--premium-gray-800);
  background: var(--premium-white);
}

/* Premium Typography Classes */
.premium-heading-1 {
  font-size: clamp(2.5rem, 5vw, 4.5rem);
  font-weight: 800;
  line-height: 1.1;
  letter-spacing: -0.025em;
  font-family: var(--premium-font-display);
}

.premium-heading-2 {
  font-size: clamp(2rem, 4vw, 3.5rem);
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: -0.02em;
  font-family: var(--premium-font-display);
}

.premium-heading-3 {
  font-size: clamp(1.5rem, 3vw, 2.25rem);
  font-weight: 600;
  line-height: 1.3;
  letter-spacing: -0.015em;
}

.premium-body-large {
  font-size: 1.25rem;
  font-weight: 400;
  line-height: 1.7;
}

.premium-body {
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.6;
}

.premium-body-small {
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.5;
}

/* Premium Button Styles */
.premium-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--premium-space-3) var(--premium-space-6);
  font-weight: 600;
  font-size: 1rem;
  line-height: 1.5;
  border-radius: var(--premium-radius-lg);
  transition: all var(--premium-transition-normal);
  cursor: pointer;
  border: none;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.premium-btn-primary {
  background: var(--premium-gradient-primary);
  color: var(--premium-white);
  box-shadow: var(--premium-shadow-md);
}

.premium-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--premium-shadow-xl);
}

.premium-btn-secondary {
  background: var(--premium-white);
  color: var(--premium-primary);
  border: 2px solid var(--premium-primary);
  box-shadow: var(--premium-shadow-sm);
}

.premium-btn-secondary:hover {
  background: var(--premium-primary);
  color: var(--premium-white);
  transform: translateY(-1px);
  box-shadow: var(--premium-shadow-lg);
}

.premium-btn-large {
  padding: var(--premium-space-4) var(--premium-space-8);
  font-size: 1.125rem;
}

/* Premium Card Styles */
.premium-card {
  background: var(--premium-white);
  border-radius: var(--premium-radius-2xl);
  box-shadow: var(--premium-shadow-lg);
  padding: var(--premium-space-8);
  transition: all var(--premium-transition-normal);
  border: 1px solid var(--premium-gray-200);
}

.premium-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--premium-shadow-2xl);
}

.premium-card-glass {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Premium Gradient Text */
.premium-gradient-text {
  background: var(--premium-gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Premium Animations */
@keyframes premium-float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes premium-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

@keyframes premium-shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.premium-animate-float {
  animation: premium-float 3s ease-in-out infinite;
}

.premium-animate-pulse {
  animation: premium-pulse 2s ease-in-out infinite;
}

/* Premium Utility Classes */
.premium-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--premium-space-6);
}

.premium-section {
  padding: var(--premium-space-24) 0;
}

.premium-grid {
  display: grid;
  gap: var(--premium-space-8);
}

.premium-flex {
  display: flex;
  align-items: center;
  gap: var(--premium-space-4);
}

.premium-text-center {
  text-align: center;
}

.premium-mb-4 { margin-bottom: var(--premium-space-4); }
.premium-mb-6 { margin-bottom: var(--premium-space-6); }
.premium-mb-8 { margin-bottom: var(--premium-space-8); }
.premium-mb-12 { margin-bottom: var(--premium-space-12); }
.premium-mb-16 { margin-bottom: var(--premium-space-16); }

/* Enhanced Responsive Design */
@media (min-width: 768px) {
  .premium-grid-md-2 { grid-template-columns: repeat(2, 1fr); }
  .premium-grid-md-3 { grid-template-columns: repeat(3, 1fr); }
}

@media (min-width: 1024px) {
  .premium-grid-lg-2 { grid-template-columns: repeat(2, 1fr); }
  .premium-grid-lg-3 { grid-template-columns: repeat(3, 1fr); }
  .premium-grid-lg-4 { grid-template-columns: repeat(4, 1fr); }
  .premium-grid-lg-5 { grid-template-columns: repeat(5, 1fr); }
  .premium-grid-lg-6 { grid-template-columns: repeat(6, 1fr); }
}

/* Mobile Optimizations */
@media (max-width: 767px) {
  .premium-heading-1 {
    font-size: clamp(2rem, 8vw, 3rem);
  }

  .premium-heading-2 {
    font-size: clamp(1.5rem, 6vw, 2.25rem);
  }

  .premium-heading-3 {
    font-size: clamp(1.25rem, 5vw, 1.75rem);
  }

  .premium-card {
    padding: var(--premium-space-6);
  }

  .premium-btn-large {
    padding: var(--premium-space-3) var(--premium-space-6);
    font-size: 1rem;
  }

  .premium-section {
    padding: var(--premium-space-16) 0;
  }
}

/* Enhanced Card Layouts */
.premium-card-fixed-height {
  min-height: 400px;
  display: flex;
  flex-direction: column;
}

.premium-card-content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.premium-card-footer {
  margin-top: auto;
}

/* Text Overflow Protection */
.premium-text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.premium-text-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.premium-text-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Additional Responsive Utilities */
.premium-hide-mobile {
  display: block;
}

.premium-show-mobile {
  display: none;
}

@media (max-width: 767px) {
  .premium-hide-mobile {
    display: none;
  }

  .premium-show-mobile {
    display: block;
  }

  .premium-stack-mobile {
    flex-direction: column;
  }

  .premium-center-mobile {
    text-align: center;
  }

  .premium-full-width-mobile {
    width: 100%;
  }
}

/* Touch-friendly interactions */
@media (hover: none) and (pointer: coarse) {
  .premium-btn:hover {
    transform: none;
  }

  .premium-card:hover {
    transform: none;
  }

  .premium-btn:active {
    transform: scale(0.98);
  }

  .premium-card:active {
    transform: scale(0.99);
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .premium-card-glass {
    backdrop-filter: blur(25px);
    -webkit-backdrop-filter: blur(25px);
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .premium-animate-float,
  .premium-animate-pulse {
    animation: none;
  }

  .premium-btn,
  .premium-card {
    transition: none;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .premium-landing {
    color-scheme: dark;
  }
}
