/* DataReflow Design System */

:root {
  /* Brand Colors */
  --dataflow-primary-50: #faf5ff;
  --dataflow-primary-100: #f3e8ff;
  --dataflow-primary-200: #e9d5ff;
  --dataflow-primary-300: #d8b4fe;
  --dataflow-primary-400: #c084fc;
  --dataflow-primary-500: #a855f7;
  --dataflow-primary-600: #9333ea;
  --dataflow-primary-700: #7c3aed;
  --dataflow-primary-800: #6b21a8;
  --dataflow-primary-900: #581c87;

  --dataflow-secondary-50: #eff6ff;
  --dataflow-secondary-100: #dbeafe;
  --dataflow-secondary-200: #bfdbfe;
  --dataflow-secondary-300: #93c5fd;
  --dataflow-secondary-400: #60a5fa;
  --dataflow-secondary-500: #3b82f6;
  --dataflow-secondary-600: #2563eb;
  --dataflow-secondary-700: #1d4ed8;
  --dataflow-secondary-800: #1e40af;
  --dataflow-secondary-900: #1e3a8a;

  /* Accent Colors */
  --dataflow-success-500: #10b981;
  --dataflow-warning-500: #f59e0b;
  --dataflow-error-500: #ef4444;
  --dataflow-info-500: #06b6d4;

  /* Neutral Colors */
  --dataflow-gray-50: #f9fafb;
  --dataflow-gray-100: #f3f4f6;
  --dataflow-gray-200: #e5e7eb;
  --dataflow-gray-300: #d1d5db;
  --dataflow-gray-400: #9ca3af;
  --dataflow-gray-500: #6b7280;
  --dataflow-gray-600: #4b5563;
  --dataflow-gray-700: #374151;
  --dataflow-gray-800: #1f2937;
  --dataflow-gray-900: #111827;

  /* Typography Scale */
  --dataflow-text-xs: 0.75rem;
  --dataflow-text-sm: 0.875rem;
  --dataflow-text-base: 1rem;
  --dataflow-text-lg: 1.125rem;
  --dataflow-text-xl: 1.25rem;
  --dataflow-text-2xl: 1.5rem;
  --dataflow-text-3xl: 1.875rem;
  --dataflow-text-4xl: 2.25rem;
  --dataflow-text-5xl: 3rem;
  --dataflow-text-6xl: 3.75rem;

  /* Font Weights */
  --dataflow-font-light: 300;
  --dataflow-font-normal: 400;
  --dataflow-font-medium: 500;
  --dataflow-font-semibold: 600;
  --dataflow-font-bold: 700;

  /* Spacing Scale */
  --dataflow-space-xs: 0.25rem;
  --dataflow-space-sm: 0.5rem;
  --dataflow-space-md: 1rem;
  --dataflow-space-lg: 1.5rem;
  --dataflow-space-xl: 2rem;
  --dataflow-space-2xl: 3rem;
  --dataflow-space-3xl: 4rem;

  /* Border Radius */
  --dataflow-radius-sm: 0.25rem;
  --dataflow-radius-md: 0.375rem;
  --dataflow-radius-lg: 0.5rem;
  --dataflow-radius-xl: 0.75rem;
  --dataflow-radius-2xl: 1rem;
  --dataflow-radius-full: 9999px;

  /* Shadows */
  --dataflow-shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --dataflow-shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --dataflow-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --dataflow-shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Transitions */
  --dataflow-transition-fast: 150ms ease-in-out;
  --dataflow-transition-normal: 300ms ease-in-out;
  --dataflow-transition-slow: 500ms ease-in-out;

  /* Z-Index Scale */
  --dataflow-z-dropdown: 1000;
  --dataflow-z-sticky: 1020;
  --dataflow-z-fixed: 1030;
  --dataflow-z-modal-backdrop: 1040;
  --dataflow-z-modal: 1050;
  --dataflow-z-popover: 1060;
  --dataflow-z-tooltip: 1070;
  --dataflow-z-toast: 1080;
}

/* Component Base Classes */
.dataflow-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--dataflow-radius-lg);
  font-weight: var(--dataflow-font-semibold);
  transition: all var(--dataflow-transition-fast);
  text-decoration: none;
  cursor: pointer;
  border: none;
  outline: none;
}

.dataflow-btn:focus {
  outline: 2px solid var(--dataflow-primary-500);
  outline-offset: 2px;
}

.dataflow-btn-primary {
  background: linear-gradient(135deg, var(--dataflow-primary-600), var(--dataflow-secondary-600));
  color: white;
  padding: 0.75rem 1.5rem;
  font-size: var(--dataflow-text-base);
}

.dataflow-btn-primary:hover {
  background: linear-gradient(135deg, var(--dataflow-primary-700), var(--dataflow-secondary-700));
  transform: translateY(-1px);
  box-shadow: var(--dataflow-shadow-lg);
}

.dataflow-btn-secondary {
  background: white;
  color: var(--dataflow-gray-700);
  border: 1px solid var(--dataflow-gray-300);
  padding: 0.75rem 1.5rem;
  font-size: var(--dataflow-text-base);
}

.dataflow-btn-secondary:hover {
  background: var(--dataflow-gray-50);
  border-color: var(--dataflow-gray-400);
}

.dataflow-btn-ghost {
  background: transparent;
  color: var(--dataflow-gray-600);
  padding: 0.5rem 1rem;
  font-size: var(--dataflow-text-sm);
}

.dataflow-btn-ghost:hover {
  background: var(--dataflow-gray-100);
  color: var(--dataflow-gray-800);
}

/* Card Components */
.dataflow-card {
  background: white;
  border-radius: var(--dataflow-radius-xl);
  box-shadow: var(--dataflow-shadow-md);
  overflow: hidden;
  transition: all var(--dataflow-transition-normal);
}

.dataflow-card:hover {
  box-shadow: var(--dataflow-shadow-xl);
  transform: translateY(-2px);
}

.dataflow-card-feature {
  padding: var(--dataflow-space-2xl);
  border-radius: var(--dataflow-radius-2xl);
  background: white;
  box-shadow: var(--dataflow-shadow-lg);
  transition: all var(--dataflow-transition-normal);
}

.dataflow-card-feature:hover {
  box-shadow: var(--dataflow-shadow-xl);
  transform: translateY(-4px);
}

/* Form Elements */
.dataflow-input {
  display: block;
  width: 100%;
  padding: 0.75rem 1rem;
  font-size: var(--dataflow-text-base);
  border: 1px solid var(--dataflow-gray-300);
  border-radius: var(--dataflow-radius-lg);
  background: white;
  transition: all var(--dataflow-transition-fast);
}

.dataflow-input:focus {
  outline: none;
  border-color: var(--dataflow-primary-500);
  box-shadow: 0 0 0 3px rgb(168 85 247 / 0.1);
}

.dataflow-label {
  display: block;
  font-size: var(--dataflow-text-sm);
  font-weight: var(--dataflow-font-medium);
  color: var(--dataflow-gray-700);
  margin-bottom: 0.5rem;
}

/* Navigation */
.dataflow-nav-link {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem;
  border-radius: var(--dataflow-radius-md);
  font-size: var(--dataflow-text-sm);
  font-weight: var(--dataflow-font-semibold);
  color: var(--dataflow-gray-700);
  text-decoration: none;
  transition: all var(--dataflow-transition-fast);
}

.dataflow-nav-link:hover {
  background: var(--dataflow-gray-50);
  color: var(--dataflow-primary-600);
}

.dataflow-nav-link.active {
  background: var(--dataflow-gray-50);
  color: var(--dataflow-primary-600);
}

/* Utility Classes */
.dataflow-gradient-primary {
  background: linear-gradient(135deg, var(--dataflow-primary-600), var(--dataflow-secondary-600));
}

.dataflow-gradient-hero {
  background: linear-gradient(135deg, var(--dataflow-primary-900), var(--dataflow-secondary-900), var(--dataflow-primary-800));
}

.dataflow-text-gradient {
  background: linear-gradient(135deg, var(--dataflow-primary-600), var(--dataflow-secondary-600));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.dataflow-shadow-glow {
  box-shadow: 0 0 20px rgb(168 85 247 / 0.3);
}

/* Animation Classes */
.dataflow-fade-in {
  animation: dataflowFadeIn var(--dataflow-transition-normal) ease-out;
}

.dataflow-slide-up {
  animation: dataflowSlideUp var(--dataflow-transition-normal) ease-out;
}

.dataflow-scale-in {
  animation: dataflowScaleIn var(--dataflow-transition-fast) ease-out;
}

@keyframes dataflowFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes dataflowSlideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes dataflowScaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Responsive Typography */
@media (max-width: 640px) {
  :root {
    --dataflow-text-4xl: 1.875rem;
    --dataflow-text-5xl: 2.25rem;
    --dataflow-text-6xl: 2.5rem;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  :root {
    --dataflow-gray-50: #1f2937;
    --dataflow-gray-100: #374151;
    --dataflow-gray-200: #4b5563;
    --dataflow-gray-300: #6b7280;
    --dataflow-gray-400: #9ca3af;
    --dataflow-gray-500: #d1d5db;
    --dataflow-gray-600: #e5e7eb;
    --dataflow-gray-700: #f3f4f6;
    --dataflow-gray-800: #f9fafb;
    --dataflow-gray-900: #ffffff;
  }
}

/* Print Styles */
@media print {
  .dataflow-no-print {
    display: none !important;
  }
  
  .dataflow-card {
    box-shadow: none;
    border: 1px solid var(--dataflow-gray-300);
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .dataflow-btn-primary {
    background: var(--dataflow-primary-800);
    border: 2px solid var(--dataflow-primary-900);
  }
  
  .dataflow-input {
    border-width: 2px;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}