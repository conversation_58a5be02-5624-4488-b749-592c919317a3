<% content_for :title, "Data Sources" %>

<!-- Upcube-inspired Data Sources Page -->
<div data-controller="data-sources-realtime" class="min-h-screen bg-gray-50">
  
  <!-- Page Header -->
  <div class="mb-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">Data Sources</h1>
        <p class="mt-2 text-gray-600">
          Connect, manage, and monitor your business data integrations
        </p>
      </div>
      <div class="mt-4 sm:mt-0 sm:ml-4">
        <%= link_to new_data_source_path, 
            class: "inline-flex items-center px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-lg hover:bg-indigo-700 transition-colors duration-200" do %>
          <svg class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
          </svg>
          Add Data Source
        <% end %>
      </div>
    </div>
  </div>

  <!-- Stats Overview Cards -->
  <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 mb-8">
    
    <!-- Connected Sources Card -->
    <div class="bg-white rounded-lg border border-gray-200 p-6">
      <div class="flex items-center justify-between mb-4">
        <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-green-100">
          <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.19 8.688a4.5 4.5 0 011.242 7.244l-4.5 4.5a4.5 4.5 0 01-6.364-6.364l1.757-1.757m13.35-.622l1.757-1.757a4.5 4.5 0 00-6.364-6.364l-4.5 4.5a4.5 4.5 0 001.242 7.244" />
          </svg>
        </div>
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
          <div class="h-1.5 w-1.5 bg-green-500 rounded-full mr-1"></div>
          Active
        </span>
      </div>
      <div>
        <p class="text-sm font-medium text-gray-600 mb-1">Connected</p>
        <p class="text-3xl font-bold text-gray-900" data-data-sources-realtime-target="connectedCount"><%= @connected_sources.count %></p>
        <p class="text-sm text-gray-500">Sources actively syncing</p>
      </div>
    </div>

    <!-- Syncing Sources Card -->
    <div class="bg-white rounded-lg border border-gray-200 p-6">
      <div class="flex items-center justify-between mb-4">
        <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-100">
          <svg class="h-6 w-6 text-blue-600 animate-spin" fill="none" viewBox="0 0 24 24" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99" />
          </svg>
        </div>
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          <div class="h-1.5 w-1.5 bg-blue-500 rounded-full mr-1 animate-pulse"></div>
          Syncing
        </span>
      </div>
      <div>
        <p class="text-sm font-medium text-gray-600 mb-1">Syncing</p>
        <p class="text-3xl font-bold text-gray-900" data-data-sources-realtime-target="syncingCount"><%= @syncing_sources.count %></p>
        <p class="text-sm text-gray-500">Currently processing data</p>
      </div>
    </div>

    <!-- Error Sources Card -->
    <div class="bg-white rounded-lg border border-gray-200 p-6">
      <div class="flex items-center justify-between mb-4">
        <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-red-100">
          <svg class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
          </svg>
        </div>
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
          <div class="h-1.5 w-1.5 bg-red-500 rounded-full mr-1"></div>
          Errors
        </span>
      </div>
      <div>
        <p class="text-sm font-medium text-gray-600 mb-1">Errors</p>
        <p class="text-3xl font-bold text-gray-900" data-data-sources-realtime-target="errorCount"><%= @error_sources.count %></p>
        <p class="text-sm text-gray-500">Sources requiring attention</p>
      </div>
    </div>

    <!-- Total Sources Card -->
    <div class="bg-white rounded-lg border border-gray-200 p-6">
      <div class="flex items-center justify-between mb-4">
        <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-purple-100">
          <svg class="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M20.25 6.375c0 2.278-3.694 4.125-8.25 4.125S3.75 8.653 3.75 6.375m16.5 0c0-2.278-3.694-4.125-8.25-4.125S3.75 4.097 3.75 6.375m16.5 0v11.25c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125V6.375m16.5 0v3.75m-16.5-3.75v3.75m16.5 0v3.75C20.25 18.653 16.556 20.5 12 20.5s-8.25-1.847-8.25-4.125v-3.75m16.5 0c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125" />
          </svg>
        </div>
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
          Total
        </span>
      </div>
      <div>
        <p class="text-sm font-medium text-gray-600 mb-1">Total Sources</p>
        <p class="text-3xl font-bold text-gray-900" data-data-sources-realtime-target="totalCount"><%= @data_sources.count %></p>
        <p class="text-sm text-gray-500">All configured integrations</p>
      </div>
    </div>
  </div>

  <!-- Filter and Search Section -->
  <div class="mb-8">
    <div class="bg-white rounded-lg border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">Filter & Search</h3>
      </div>
      <div class="p-6">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <!-- Search Input -->
          <div class="lg:col-span-2">
            <label class="block text-sm font-medium text-gray-700 mb-2">Search Sources</label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 15.803a7.5 7.5 0 0010.607 0z" />
                </svg>
              </div>
              <input type="text" placeholder="Search by name, platform, or status..." 
                     class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                     data-data-sources-realtime-target="searchInput">
            </div>
          </div>
          
          <!-- Status Filter -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Filter by Status</label>
            <select class="block w-full py-2 px-3 border border-gray-300 rounded-lg text-gray-900 focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    data-data-sources-realtime-target="statusFilter">
              <option value="">All Statuses</option>
              <option value="connected">Connected</option>
              <option value="syncing">Syncing</option>
              <option value="error">Error</option>
              <option value="disconnected">Disconnected</option>
            </select>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Data Sources Grid -->
  <div data-data-sources-realtime-target="sourcesContainer">
    <% if @data_sources.any? %>
      <div class="grid grid-cols-1 gap-6 lg:grid-cols-2 xl:grid-cols-3">
        <% @data_sources.each do |data_source| %>
          <div class="bg-white rounded-lg border border-gray-200 hover:shadow-lg transition-shadow duration-200"
               data-source-id="<%= data_source.id %>"
               data-source-name="<%= data_source.name.downcase %>"
               data-source-platform="<%= data_source.source_type %>"
               data-source-status="<%= data_source.status %>">
            
            <div class="p-6">
              <!-- Source Header -->
              <div class="flex items-start justify-between mb-4">
                <div class="flex items-center gap-3">
                  <div class="h-12 w-12 rounded-lg <%= case data_source.source_type
                    when 'shopify' then 'bg-green-100 text-green-600'
                    when 'stripe' then 'bg-purple-100 text-purple-600'
                    when 'google_analytics' then 'bg-orange-100 text-orange-600'
                    when 'quickbooks' then 'bg-blue-100 text-blue-600'
                    when 'mailchimp' then 'bg-yellow-100 text-yellow-600'
                    else 'bg-gray-100 text-gray-600'
                    end %> flex items-center justify-center text-sm font-semibold">
                    <%= data_source.source_type.first(2).upcase %>
                  </div>
                  <div>
                    <h3 class="text-lg font-semibold text-gray-900">
                      <%= link_to data_source.name, data_source_path(data_source), class: "hover:text-indigo-600" %>
                    </h3>
                    <p class="text-sm text-gray-500 capitalize"><%= data_source.source_type.humanize %></p>
                  </div>
                </div>
                
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium <%= case data_source.status
                  when 'connected' then 'bg-green-100 text-green-800'
                  when 'syncing' then 'bg-blue-100 text-blue-800'
                  when 'error' then 'bg-red-100 text-red-800'
                  else 'bg-gray-100 text-gray-800'
                  end %>">
                  <%= data_source.status.humanize %>
                </span>
              </div>

              <!-- Metrics -->
              <div class="grid grid-cols-2 gap-4 mb-4">
                <div class="text-center p-3 bg-gray-50 rounded-lg">
                  <div class="text-sm font-medium text-gray-500">Records</div>
                  <div class="text-lg font-semibold text-gray-900"><%= number_with_delimiter(data_source.raw_data_records.count) %></div>
                </div>
                <div class="text-center p-3 bg-gray-50 rounded-lg">
                  <div class="text-sm font-medium text-gray-500">Last Sync</div>
                  <div class="text-sm font-semibold text-gray-900">
                    <%= data_source.extraction_jobs.completed.last&.completed_at ? 
                        time_ago_in_words(data_source.extraction_jobs.completed.last.completed_at) + " ago" : 
                        "Never" %>
                  </div>
                </div>
              </div>

              <!-- Action Buttons -->
              <div class="flex flex-wrap gap-2">
                <% if data_source.status == 'connected' %>
                  <%= link_to sync_now_data_source_path(data_source), method: :post,
                      class: "inline-flex items-center gap-1 px-3 py-2 bg-indigo-600 text-white text-sm font-medium rounded-lg hover:bg-indigo-700 transition-colors",
                      data: { confirm: "Trigger manual sync for #{data_source.name}?" } do %>
                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99" />
                    </svg>
                    Sync Now
                  <% end %>
                <% end %>
                
                <%= link_to edit_data_source_path(data_source),
                    class: "inline-flex items-center gap-1 px-3 py-2 bg-white text-gray-700 text-sm font-medium rounded-lg border border-gray-300 hover:bg-gray-50 transition-colors" do %>
                  <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10" />
                  </svg>
                  Edit
                <% end %>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    <% else %>
      <!-- Empty State -->
      <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.25 6.375c0 2.278-3.694 4.125-8.25 4.125S3.75 8.653 3.75 6.375m16.5 0c0-2.278-3.694-4.125-8.25-4.125S3.75 4.097 3.75 6.375m16.5 0v11.25c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125V6.375m16.5 0v3.75m-16.5-3.75v3.75m16.5 0v3.75C20.25 18.653 16.556 20.5 12 20.5s-8.25-1.847-8.25-4.125v-3.75m16.5 0c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No data sources</h3>
        <p class="mt-1 text-sm text-gray-500">Get started by connecting your first data source.</p>
        <div class="mt-6">
          <%= link_to new_data_source_path, 
              class: "inline-flex items-center px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-lg hover:bg-indigo-700" do %>
            <svg class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
            </svg>
            Add Data Source
          <% end %>
        </div>
      </div>
    <% end %>
  </div>
</div>