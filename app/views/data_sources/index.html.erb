<% content_for :page_title, "Data Sources" %>

<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
  <!-- Premium Header with Glassmorphism -->
  <div class="relative bg-white/80 backdrop-blur-xl border-b border-white/20 shadow-xl">
    <div class="absolute inset-0 bg-gradient-to-r from-blue-600/10 to-indigo-600/10"></div>
    <div class="relative px-4 sm:px-6 lg:px-8 py-8">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div class="space-y-2">
          <div class="flex items-center space-x-3">
            <div class="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg">
              <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
              </svg>
            </div>
            <h1 class="text-3xl font-bold bg-gradient-to-r from-gray-900 via-blue-900 to-indigo-900 bg-clip-text text-transparent">
              Data Sources
            </h1>
          </div>
          <p class="text-slate-600 font-medium">Connect, manage, and monitor your business data integrations</p>
        </div>
        <div class="mt-4 sm:mt-0 flex items-center space-x-4">
          <div class="flex items-center space-x-2 px-4 py-2 bg-green-100/80 backdrop-blur-sm rounded-full border border-green-200/50">
            <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span class="text-green-700 text-sm font-semibold">Live</span>
          </div>
          <%= link_to new_data_source_path, class: "group inline-flex items-center px-6 py-3 border border-slate-300/50 rounded-xl shadow-lg text-sm font-semibold text-slate-700 bg-white/70 backdrop-blur-sm hover:bg-white/90 hover:shadow-xl transition-all duration-300 hover:-translate-y-0.5" do %>
            <svg class="h-4 w-4 mr-2 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
            </svg>
            Add Data Source
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <!-- Premium Stats Overview Cards -->
  <div class="px-4 sm:px-6 lg:px-8 py-8">
    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
      <!-- Connected Sources Card -->
      <div class="group relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl hover:-translate-y-1 transition-all duration-500">
        <div class="absolute inset-0 bg-gradient-to-br from-green-500/5 to-emerald-500/5 rounded-2xl"></div>
        <div class="relative">
          <div class="flex items-center space-x-4 mb-4">
            <div class="p-3 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl shadow-lg group-hover:shadow-green-500/25 transition-all duration-300">
              <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.19 8.688a4.5 4.5 0 011.242 7.244l-4.5 4.5a4.5 4.5 0 01-6.364-6.364l1.757-1.757m13.35-.622l1.757-1.757a4.5 4.5 0 00-6.364-6.364l-4.5 4.5a4.5 4.5 0 001.242 7.244" />
              </svg>
            </div>
            <div class="flex-1">
              <h3 class="text-sm font-semibold text-slate-600 uppercase tracking-wider">Connected</h3>
              <div class="text-3xl font-bold text-slate-900 mt-1" data-data-sources-realtime-target="connectedCount"><%= @connected_sources.count %></div>
            </div>
          </div>
          <div class="flex items-center space-x-2 text-xs text-slate-500">
            <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span>Sources actively syncing</span>
          </div>
        </div>
      </div>

      <!-- Syncing Sources Card -->
      <div class="group relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl hover:-translate-y-1 transition-all duration-500">
        <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-indigo-500/5 rounded-2xl"></div>
        <div class="relative">
          <div class="flex items-center space-x-4 mb-4">
            <div class="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg group-hover:shadow-blue-500/25 transition-all duration-300">
              <svg class="h-6 w-6 text-white animate-spin" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99" />
              </svg>
            </div>
            <div class="flex-1">
              <h3 class="text-sm font-semibold text-slate-600 uppercase tracking-wider">Syncing</h3>
              <div class="text-3xl font-bold text-slate-900 mt-1" data-data-sources-realtime-target="syncingCount"><%= @syncing_sources.count %></div>
            </div>
          </div>
          <div class="flex items-center space-x-2 text-xs text-slate-500">
            <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
            <span>Currently processing data</span>
          </div>
        </div>
      </div>

      <!-- Error Sources Card -->
      <div class="group relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl hover:-translate-y-1 transition-all duration-500">
        <div class="absolute inset-0 bg-gradient-to-br from-red-500/5 to-rose-500/5 rounded-2xl"></div>
        <div class="relative">
          <div class="flex items-center space-x-4 mb-4">
            <div class="p-3 bg-gradient-to-br from-red-500 to-rose-600 rounded-xl shadow-lg group-hover:shadow-red-500/25 transition-all duration-300">
              <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
              </svg>
            </div>
            <div class="flex-1">
              <h3 class="text-sm font-semibold text-slate-600 uppercase tracking-wider">Errors</h3>
              <div class="text-3xl font-bold text-slate-900 mt-1" data-data-sources-realtime-target="errorCount"><%= @error_sources.count %></div>
            </div>
          </div>
          <div class="flex items-center space-x-2 text-xs text-slate-500">
            <div class="w-2 h-2 bg-red-500 rounded-full"></div>
            <span>Sources requiring attention</span>
          </div>
        </div>
      </div>

      <!-- Total Sources Card -->
      <div class="group relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl hover:-translate-y-1 transition-all duration-500">
        <div class="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-indigo-500/5 rounded-2xl"></div>
        <div class="relative">
          <div class="flex items-center space-x-4 mb-4">
            <div class="p-3 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl shadow-lg group-hover:shadow-purple-500/25 transition-all duration-300">
              <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M20.25 6.375c0 2.278-3.694 4.125-8.25 4.125S3.75 8.653 3.75 6.375m16.5 0c0-2.278-3.694-4.125-8.25-4.125S3.75 4.097 3.75 6.375m16.5 0v11.25c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125V6.375m16.5 0v3.75m-16.5-3.75v3.75m16.5 0v3.75C20.25 16.153 16.556 18 12 18s-8.25-1.847-8.25-4.125v-3.75m16.5 0c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125" />
              </svg>
            </div>
            <div class="flex-1">
              <h3 class="text-sm font-semibold text-slate-600 uppercase tracking-wider">Total Sources</h3>
              <div class="text-3xl font-bold text-slate-900 mt-1" data-data-sources-realtime-target="totalCount"><%= @data_sources.count %></div>
            </div>
          </div>
          <div class="flex items-center space-x-2 text-xs text-slate-500">
            <span>All configured integrations</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Premium Filter and Search Section -->
  <div class="px-4 sm:px-6 lg:px-8 pb-8">
    <div class="relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 overflow-hidden hover:shadow-2xl transition-all duration-500">
      <div class="absolute inset-0 bg-gradient-to-br from-slate-500/5 to-gray-500/5"></div>
      <div class="relative">
        <div class="px-6 py-5 border-b border-slate-200/50 bg-gradient-to-r from-slate-50/50 to-gray-50/50">
          <div class="flex items-center space-x-3">
            <div class="p-2 bg-gradient-to-br from-slate-600 to-gray-700 rounded-lg shadow-lg">
              <svg class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
              </svg>
            </div>
            <h3 class="text-lg font-bold text-slate-900">Filter & Search</h3>
          </div>
        </div>
        <div class="p-6">
          <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Search Input -->
            <div class="lg:col-span-2">
              <label class="block text-sm font-semibold text-slate-700 mb-2">Search Sources</label>
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg class="h-5 w-5 text-slate-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 15.803a7.5 7.5 0 0010.607 0z" />
                  </svg>
                </div>
                <input type="text" placeholder="Search by name, platform, or status..." 
                       class="block w-full pl-10 pr-3 py-3 border border-slate-300/50 rounded-xl text-gray-900 placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent backdrop-blur-sm bg-white/70 shadow-lg transition-all duration-300 focus:shadow-xl"
                       data-data-sources-realtime-target="searchInput">
              </div>
            </div>
            
            <!-- Status Filter -->
            <div>
              <label class="block text-sm font-semibold text-slate-700 mb-2">Filter by Status</label>
              <select class="block w-full py-3 px-3 border border-slate-300/50 rounded-xl text-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent backdrop-blur-sm bg-white/70 shadow-lg transition-all duration-300 focus:shadow-xl"
                      data-data-sources-realtime-target="statusFilter">
                <option value="">All Statuses</option>
                <option value="connected">Connected</option>
                <option value="syncing">Syncing</option>
                <option value="error">Error</option>
                <option value="disconnected">Disconnected</option>
              </select>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Premium Data Sources Grid -->
  <div class="px-4 sm:px-6 lg:px-8 pb-8" data-controller="data-sources-realtime">
    <div data-data-sources-realtime-target="sourcesContainer">
      <% if @data_sources.any? %>
        <div class="grid grid-cols-1 gap-6 lg:grid-cols-2 xl:grid-cols-3">
          <% @data_sources.each do |data_source| %>
            <div class="group relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 overflow-hidden hover:shadow-2xl hover:-translate-y-1 transition-all duration-500"
                 data-source-id="<%= data_source.id %>"
                 data-source-name="<%= data_source.name.downcase %>"
                 data-source-platform="<%= data_source.source_type %>"
                 data-source-status="<%= data_source.status %>">
              <div class="absolute inset-0 bg-gradient-to-br from-<%= case data_source.source_type
                when 'shopify' then 'green'
                when 'stripe' then 'purple'
                when 'google_analytics' then 'orange'
                when 'quickbooks' then 'blue'
                when 'mailchimp' then 'yellow'
                else 'gray'
                end %>-500/5 to-<%= case data_source.source_type
                when 'shopify' then 'emerald'
                when 'stripe' then 'indigo'
                when 'google_analytics' then 'red'
                when 'quickbooks' then 'indigo'
                when 'mailchimp' then 'amber'
                else 'slate'
                end %>-500/5"></div>
              
              <div class="relative p-6">
                <!-- Source Header -->
                <div class="flex items-start justify-between mb-4">
                  <div class="flex items-center gap-3">
                    <div class="p-3 bg-gradient-to-br from-<%= case data_source.source_type
                      when 'shopify' then 'green-500 to-emerald-600'
                      when 'stripe' then 'purple-500 to-indigo-600'
                      when 'google_analytics' then 'orange-500 to-red-600'
                      when 'quickbooks' then 'blue-500 to-indigo-600'
                      when 'mailchimp' then 'yellow-500 to-amber-600'
                      else 'gray-500 to-slate-600'
                      end %> rounded-xl shadow-lg group-hover:shadow-<%= case data_source.source_type
                      when 'shopify' then 'green'
                      when 'stripe' then 'purple'
                      when 'google_analytics' then 'orange'
                      when 'quickbooks' then 'blue'
                      when 'mailchimp' then 'yellow'
                      else 'gray'
                      end %>-500/25 transition-all duration-300">
                      <span class="text-white text-sm font-bold">
                        <%= data_source.source_type.first(2).upcase %>
                      </span>
                    </div>
                    <div>
                      <h3 class="text-lg font-bold text-slate-900 group-hover:text-indigo-600 transition-colors">
                        <%= link_to data_source.name, data_source_path(data_source), class: "hover:text-indigo-600" %>
                      </h3>
                      <p class="text-sm text-slate-500 capitalize"><%= data_source.source_type.humanize %></p>
                    </div>
                  </div>
                  
                  <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold <%= case data_source.status
                    when 'connected' then 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 border border-green-200/50'
                    when 'syncing' then 'bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 border border-blue-200/50'
                    when 'error' then 'bg-gradient-to-r from-red-100 to-rose-100 text-red-800 border border-red-200/50'
                    else 'bg-gradient-to-r from-gray-100 to-slate-100 text-gray-800 border border-gray-200/50'
                    end %> shadow-sm">
                    <% if data_source.status == 'syncing' %>
                      <svg class="mr-1 h-3 w-3 animate-spin" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                    <% end %>
                    <%= data_source.status.humanize %>
                  </span>
                </div>

                <!-- Metrics -->
                <div class="grid grid-cols-2 gap-4 mb-4">
                  <div class="text-center p-3 bg-gradient-to-r from-slate-50/50 to-white/50 rounded-xl border border-slate-200/50">
                    <div class="text-sm font-semibold text-slate-600">Records</div>
                    <div class="text-2xl font-bold text-slate-900 mt-1"><%= number_with_delimiter(data_source.raw_data_records.count) %></div>
                  </div>
                  <div class="text-center p-3 bg-gradient-to-r from-slate-50/50 to-white/50 rounded-xl border border-slate-200/50">
                    <div class="text-sm font-semibold text-slate-600">Last Sync</div>
                    <div class="text-sm font-bold text-slate-900 mt-1">
                      <%= data_source.extraction_jobs.completed.last&.completed_at ? 
                          time_ago_in_words(data_source.extraction_jobs.completed.last.completed_at) + " ago" : 
                          "Never" %>
                    </div>
                  </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-wrap gap-2">
                  <% if data_source.status == 'connected' %>
                    <%= link_to sync_now_data_source_path(data_source), method: :post,
                        class: "group/btn inline-flex items-center gap-1 px-4 py-2 bg-gradient-to-r from-indigo-500 to-purple-600 text-white text-sm font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-0.5",
                        data: { confirm: "Trigger manual sync for #{data_source.name}?" } do %>
                      <svg class="h-4 w-4 group-hover/btn:rotate-180 transition-transform duration-500" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99" />
                      </svg>
                      Sync Now
                    <% end %>
                  <% end %>
                  
                  <%= link_to edit_data_source_path(data_source),
                      class: "group/btn inline-flex items-center gap-1 px-4 py-2 bg-white/70 text-slate-700 text-sm font-semibold rounded-xl border border-slate-300/50 shadow-lg backdrop-blur-sm hover:bg-white/90 hover:shadow-xl transition-all duration-300 hover:-translate-y-0.5" do %>
                    <svg class="h-4 w-4 group-hover/btn:scale-110 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10" />
                    </svg>
                    Edit
                  <% end %>
                </div>
              </div>
            </div>
          <% end %>
        </div>
      <% else %>
        <!-- Premium Empty State -->
        <div class="text-center py-16">
          <div class="mx-auto w-24 h-24 bg-gradient-to-br from-slate-100 to-slate-200 rounded-full flex items-center justify-center mb-6 shadow-xl">
            <svg class="h-12 w-12 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.25 6.375c0 2.278-3.694 4.125-8.25 4.125S3.75 8.653 3.75 6.375m16.5 0c0-2.278-3.694-4.125-8.25-4.125S3.75 4.097 3.75 6.375m16.5 0v11.25c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125V6.375m16.5 0v3.75m-16.5-3.75v3.75m16.5 0v3.75C20.25 16.153 16.556 18 12 18s-8.25-1.847-8.25-4.125v-3.75m16.5 0c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125" />
            </svg>
          </div>
          <h3 class="text-xl font-bold text-slate-900 mb-2">No data sources yet</h3>
          <p class="text-slate-500 mb-8">Get started by connecting your first data source.</p>
          <%= link_to new_data_source_path, 
              class: "inline-flex items-center px-6 py-3 bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-0.5" do %>
            <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
            </svg>
            Add Data Source
          <% end %>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Live Update Indicator -->
  <div class="fixed bottom-6 right-6 z-50">
    <div class="flex items-center space-x-2 px-4 py-2 bg-white/90 backdrop-blur-xl rounded-full shadow-xl border border-white/20">
      <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
      <span class="text-xs font-semibold text-slate-700">Live Updates</span>
    </div>
  </div>
</div>

<!-- Enhanced JavaScript for Premium UX -->
<script>
  // Add smooth scroll behavior
  document.documentElement.style.scrollBehavior = 'smooth';

  // Add intersection observer for card animations
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  };

  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.style.opacity = '1';
        entry.target.style.transform = 'translateY(0)';
      }
    });
  }, observerOptions);

  // Observe all cards for animation
  document.querySelectorAll('.group').forEach(card => {
    card.style.opacity = '0';
    card.style.transform = 'translateY(20px)';
    card.style.transition = 'opacity 0.6s ease-out, transform 0.6s ease-out';
    observer.observe(card);
  });
</script>