<% content_for :title, @data_source.name %>

<div class="px-4 sm:px-6 lg:px-8">
  <div class="sm:flex sm:items-center">
    <div class="sm:flex-auto">
      <div class="flex items-center gap-4">
        <div class="flex h-12 w-12 shrink-0 items-center justify-center rounded-lg bg-<%= 
          case @data_source.source_type
          when 'shopify' then 'green'
          when 'woocommerce' then 'purple'
          when 'amazon_seller_central' then 'orange'
          when 'quickbooks' then 'blue'
          when 'stripe' then 'indigo'
          else 'gray'
          end
        %>-600 text-white">
          <span class="text-lg font-bold">
            <%= @data_source.source_type.first.upcase %>
          </span>
        </div>
        <div>
          <h1 class="text-2xl font-semibold leading-6 text-gray-900"><%= @data_source.name %></h1>
          <div class="flex items-center gap-3 mt-1">
            <span class="inline-flex items-center rounded-md bg-<%= 
              case @data_source.status
              when 'connected' then 'green'
              when 'syncing' then 'blue'
              when 'error' then 'red'
              when 'disconnected' then 'gray'
              else 'gray'
              end
            %>-50 px-2 py-1 text-xs font-medium text-<%= 
              case @data_source.status
              when 'connected' then 'green'
              when 'syncing' then 'blue'
              when 'error' then 'red'
              when 'disconnected' then 'gray'
              else 'gray'
              end
            %>-700 ring-1 ring-inset ring-<%= 
              case @data_source.status
              when 'connected' then 'green'
              when 'syncing' then 'blue'
              when 'error' then 'red'
              when 'disconnected' then 'gray'
              else 'gray'
              end
            %>-600/20">
              <%= @data_source.status.humanize %>
            </span>
            <span class="text-sm text-gray-500"><%= @data_source.source_type.humanize %></span>
          </div>
        </div>
      </div>
    </div>
    <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none flex gap-3">
      <%= link_to data_sources_path, class: "block rounded-md bg-gray-600 px-3 py-2 text-center text-sm font-semibold text-white shadow-sm hover:bg-gray-500" do %>
        ← Back to Data Sources
      <% end %>
      <%= link_to edit_data_source_path(@data_source), class: "block rounded-md bg-indigo-600 px-3 py-2 text-center text-sm font-semibold text-white shadow-sm hover:bg-indigo-500" do %>
        Edit Data Source
      <% end %>
    </div>
  </div>

  <!-- Quick Stats -->
  <div class="mt-8 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
    <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl p-6">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="flex h-8 w-8 items-center justify-center rounded-lg bg-blue-600">
            <svg class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="M20.25 6.375c0 2.278-3.694 4.125-8.25 4.125S3.75 8.653 3.75 6.375m16.5 0c0-2.278-3.694-4.125-8.25-4.125S3.75 4.097 3.75 6.375m16.5 0v11.25c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125V6.375m16.5 0v3.75m-16.5-3.75v3.75m16.5 0v3.75C20.25 18.653 16.556 20.5 12 20.5s-8.25-1.847-8.25-4.125v-3.75m16.5 0c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125" />
            </svg>
          </div>
        </div>
        <div class="ml-4 flex-1">
          <p class="text-sm font-medium text-gray-500">Total Records</p>
          <p class="text-2xl font-semibold text-gray-900"><%= number_with_delimiter(@stats[:total_records]) %></p>
        </div>
      </div>
    </div>

    <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl p-6">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="flex h-8 w-8 items-center justify-center rounded-lg bg-green-600">
            <svg class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
        </div>
        <div class="ml-4 flex-1">
          <p class="text-sm font-medium text-gray-500">Successful Syncs</p>
          <p class="text-2xl font-semibold text-gray-900"><%= @stats[:successful_syncs] %></p>
        </div>
      </div>
    </div>

    <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl p-6">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="flex h-8 w-8 items-center justify-center rounded-lg bg-red-600">
            <svg class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z" />
            </svg>
          </div>
        </div>
        <div class="ml-4 flex-1">
          <p class="text-sm font-medium text-gray-500">Failed Syncs</p>
          <p class="text-2xl font-semibold text-gray-900"><%= @stats[:failed_syncs] %></p>
        </div>
      </div>
    </div>

    <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl p-6">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="flex h-8 w-8 items-center justify-center rounded-lg bg-<%= 
            case @stats[:connection_health]
            when 'good' then 'green'
            when 'fair' then 'yellow'
            when 'poor' then 'orange'
            when 'disconnected' then 'red'
            else 'gray'
            end
          %>-600">
            <svg class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z" />
            </svg>
          </div>
        </div>
        <div class="ml-4 flex-1">
          <p class="text-sm font-medium text-gray-500">Connection Health</p>
          <p class="text-2xl font-semibold text-gray-900 capitalize"><%= @stats[:connection_health].humanize %></p>
        </div>
      </div>
    </div>
  </div>

  <!-- Data Visualization Builder -->
  <% if @data_source.source_type == 'file_upload' && @processed_data.present? %>
    <div class="mt-8">
      <%= render 'visualization_builder' %>
    </div>
  <% end %>

  <!-- Main Content Grid -->
  <div class="mt-8 grid grid-cols-1 gap-8 lg:grid-cols-3">
    
    <!-- Left Column - Details and Actions -->
    <div class="lg:col-span-2 space-y-8">
      
      <!-- Data Source Details -->
      <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-base font-semibold leading-6 text-gray-900">Data Source Details</h3>
        </div>
        <div class="px-6 py-6">
          <dl class="grid grid-cols-1 gap-x-6 gap-y-6 sm:grid-cols-2">
            <div>
              <dt class="text-sm font-medium text-gray-500">Source Type</dt>
              <dd class="mt-1 text-sm text-gray-900"><%= @data_source.source_type.humanize %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">Sync Frequency</dt>
              <dd class="mt-1 text-sm text-gray-900"><%= @data_source.sync_frequency&.humanize || 'Not set' %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">Last Sync</dt>
              <dd class="mt-1 text-sm text-gray-900">
                <%= @stats[:last_sync]&.strftime("%B %d, %Y at %l:%M %p") || 'Never' %>
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">Next Sync</dt>
              <dd class="mt-1 text-sm text-gray-900">
                <%= @stats[:next_sync]&.strftime("%B %d, %Y at %l:%M %p") || 'Not scheduled' %>
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">Data Freshness</dt>
              <dd class="mt-1">
                <span class="inline-flex items-center rounded-md bg-<%= 
                  case @stats[:data_freshness]
                  when 'very_fresh' then 'green'
                  when 'fresh' then 'blue'
                  when 'moderate' then 'yellow'
                  when 'stale' then 'orange'
                  when 'very_stale' then 'red'
                  else 'gray'
                  end
                %>-50 px-2 py-1 text-xs font-medium text-<%= 
                  case @stats[:data_freshness]
                  when 'very_fresh' then 'green'
                  when 'fresh' then 'blue'
                  when 'moderate' then 'yellow'
                  when 'stale' then 'orange'
                  when 'very_stale' then 'red'
                  else 'gray'
                  end
                %>-700 ring-1 ring-inset ring-<%= 
                  case @stats[:data_freshness]
                  when 'very_fresh' then 'green'
                  when 'fresh' then 'blue'
                  when 'moderate' then 'yellow'
                  when 'stale' then 'orange'
                  when 'very_stale' then 'red'
                  else 'gray'
                  end
                %>-600/20">
                  <%= @stats[:data_freshness].humanize %>
                </span>
              </dd>
            </div>
          </dl>
          
          <% if @data_source.description.present? %>
            <div class="mt-6">
              <dt class="text-sm font-medium text-gray-500">Description</dt>
              <dd class="mt-1 text-sm text-gray-900"><%= @data_source.description %></dd>
            </div>
          <% end %>
        </div>
      </div>

      <!-- Recent Records -->
      <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-base font-semibold leading-6 text-gray-900">Recent Data Records</h3>
        </div>
        <div class="overflow-x-auto">
          <% if defined?(@recent_records) && @recent_records.any? %>
            <table class="min-w-full divide-y divide-gray-300">
              <thead class="bg-gray-50">
                <tr>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">Type</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">External ID</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">Status</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">Created</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <% @recent_records.each do |record| %>
                  <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      <%= record.record_type.humanize %>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <%= record.external_id %>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span class="inline-flex items-center rounded-md bg-<%= 
                        case record.processing_status
                        when 'processed' then 'green'
                        when 'processing' then 'blue'
                        when 'pending' then 'yellow'
                        when 'failed' then 'red'
                        else 'gray'
                        end
                      %>-50 px-2 py-1 text-xs font-medium text-<%= 
                        case record.processing_status
                        when 'processed' then 'green'
                        when 'processing' then 'blue'
                        when 'pending' then 'yellow'
                        when 'failed' then 'red'
                        else 'gray'
                        end
                      %>-700 ring-1 ring-inset ring-<%= 
                        case record.processing_status
                        when 'processed' then 'green'
                        when 'processing' then 'blue'
                        when 'pending' then 'yellow'
                        when 'failed' then 'red'
                        else 'gray'
                        end
                      %>-600/20">
                        <%= record.processing_status.humanize %>
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <%= record.created_at.strftime("%m/%d/%y %l:%M %p") %>
                    </td>
                  </tr>
                <% end %>
              </tbody>
            </table>
          <% else %>
            <div class="px-6 py-12 text-center">
              <p class="text-sm text-gray-500">No data records found for this data source.</p>
            </div>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Right Column - Actions and Recent Jobs -->
    <div class="space-y-8">
      
      <!-- Quick Actions -->
      <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-base font-semibold leading-6 text-gray-900">Quick Actions</h3>
        </div>
        <div class="px-6 py-6 space-y-4">
          <%= link_to test_connection_data_source_path(@data_source), method: :post,
              class: "flex w-full items-center justify-center gap-3 rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50" do %>
            <svg class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.236 4.53L8.53 10.96a.75.75 0 00-1.06 1.061l1.5 1.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />
            </svg>
            Test Connection
          <% end %>
          
          <% if @data_source.connected? %>
            <%= link_to sync_now_data_source_path(@data_source), method: :post,
                class: "flex w-full items-center justify-center gap-3 rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500",
                data: { confirm: "Are you sure you want to trigger a manual sync?" } do %>
              <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M15.312 11.424a5.5 5.5 0 01-9.201 2.466l-.312-.311h2.433a.75.75 0 000-1.5H3.989a.75.75 0 00-.75.75v4.242a.75.75 0 001.5 0v-2.43l.31.31a7 7 0 0011.712-*********** 0 00-1.449-.39zm1.23-3.723a.75.75 0 00.219-.53V2.929a.75.75 0 00-1.5 0V5.36l-.31-.31A7 7 0 003.239 8.188a.75.75 0 101.448.389A5.5 5.5 0 0113.89 6.11l.311.31h-2.432a.75.75 0 000 1.5h4.243a.75.75 0 00.53-.219z" clip-rule="evenodd" />
              </svg>
              Sync Now
            <% end %>
          <% end %>
          
          <%= link_to edit_data_source_path(@data_source),
              class: "flex w-full items-center justify-center gap-3 rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50" do %>
            <svg class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
              <path d="M2.695 14.763l-1.262 3.154a.5.5 0 00.65.65l3.155-1.262a4 4 0 001.343-.885L17.5 5.5a2.121 2.121 0 00-3-3L3.58 13.42a4 4 0 00-.885 1.343z" />
            </svg>
            Edit Settings
          <% end %>
          
          <%= link_to data_source_path(@data_source), method: :delete,
              class: "flex w-full items-center justify-center gap-3 rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500",
              data: { confirm: "Are you sure you want to delete this data source? This action cannot be undone." } do %>
            <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M8.75 1A2.75 2.75 0 006 3.75v.443c-.795.077-1.584.176-2.365.298a.75.75 0 10.23 1.482l.149-.022.841 10.518A2.75 2.75 0 007.596 19h4.807a2.75 2.75 0 002.742-2.53l.841-10.52.149.023a.75.75 0 00.23-1.482A41.03 41.03 0 0014 4.193V3.75A2.75 2.75 0 0011.25 1h-2.5zM10 4c.84 0 1.673.025 2.5.075V3.75c0-.69-.56-1.25-1.25-1.25h-2.5c-.69 0-1.25.56-1.25 1.25v.325C8.327 4.025 9.16 4 10 4zM8.58 7.72a.75.75 0 00-1.5.06l.3 7.5a.75.75 0 101.5-.06l-.3-7.5zm4.34.06a.75.75 0 10-1.5-.06l-.3 7.5a.75.75 0 101.5.06l.3-7.5z" clip-rule="evenodd" />
            </svg>
            Delete Source
          <% end %>
        </div>
      </div>

      <!-- Recent Extraction Jobs -->
      <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-base font-semibold leading-6 text-gray-900">Recent Jobs</h3>
        </div>
        <div class="px-6 py-6">
          <% if defined?(@recent_jobs) && @recent_jobs.any? %>
            <ul role="list" class="space-y-4">
              <% @recent_jobs.each do |job| %>
                <li class="flex items-center justify-between">
                  <div class="flex items-center gap-3">
                    <span class="inline-flex items-center rounded-md bg-<%= 
                      case job.status
                      when 'completed' then 'green'
                      when 'running' then 'blue'
                      when 'queued' then 'yellow'
                      when 'failed' then 'red'
                      else 'gray'
                      end
                    %>-50 px-2 py-1 text-xs font-medium text-<%= 
                      case job.status
                      when 'completed' then 'green'
                      when 'running' then 'blue'
                      when 'queued' then 'yellow'
                      when 'failed' then 'red'
                      else 'gray'
                      end
                    %>-700 ring-1 ring-inset ring-<%= 
                      case job.status
                      when 'completed' then 'green'
                      when 'running' then 'blue'
                      when 'queued' then 'yellow'
                      when 'failed' then 'red'
                      else 'gray'
                      end
                    %>-600/20">
                      <%= job.status.humanize %>
                    </span>
                    <div class="text-sm">
                      <p class="font-medium text-gray-900">Job #<%= job.id %></p>
                      <p class="text-gray-500">
                        <%= job.completed_at&.strftime("%m/%d/%y %l:%M %p") || job.created_at.strftime("%m/%d/%y %l:%M %p") %>
                      </p>
                    </div>
                  </div>
                  <div class="text-right text-sm">
                    <% if job.records_processed.to_i > 0 %>
                      <p class="font-medium text-gray-900"><%= job.records_processed %> records</p>
                      <% if job.records_failed.to_i > 0 %>
                        <p class="text-red-600"><%= job.records_failed %> failed</p>
                      <% end %>
                    <% end %>
                  </div>
                </li>
              <% end %>
            </ul>
          <% else %>
            <p class="text-sm text-gray-500">No extraction jobs found.</p>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>