<% content_for :title, "Connect Data Source" %>
<% content_for :page_header do %>
  <%= render 'shared/page_header', 
        title: 'Connect Data Source',
        subtitle: 'Seamlessly integrate your business data for powerful insights',
        icon: 'data-source',
        breadcrumbs: [
          { text: 'Dashboard', path: dashboard_path },
          { text: 'Data Sources', path: data_sources_path },
          { text: 'Connect New', current: true }
        ] %>
<% end %>

<%
  # Initialize data for the wizard
  @wizard_data = DataSourceWizardService.new.prepare_wizard_data
  data_source_configs = @wizard_data[:configurations]
  sync_frequencies = @wizard_data[:sync_frequencies]
  file_config = @wizard_data[:file_config]
%>

<div class="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 py-8">
  <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8" 
       data-controller="data-source-wizard auto-save" 
       data-auto-save-url-value="<%= auto_save_data_sources_path %>">
    
    <!-- Main Card Container -->
    <div class="bg-white dark:bg-gray-800 shadow-2xl rounded-3xl overflow-hidden border border-gray-200 dark:border-gray-700">
      
      <!-- Enhanced Progress Indicator -->
      <%= render 'wizard_progress' %>
      
      <!-- Auto-save Status -->
      <div class="px-8 py-2 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600">
        <div class="flex items-center justify-between">
          <div class="text-sm text-gray-600 dark:text-gray-300" data-auto-save-target="status">
            <span class="inline-flex items-center">
              <svg class="w-4 h-4 mr-1 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              All changes saved
            </span>
          </div>
          <div class="text-xs text-gray-500 dark:text-gray-400" data-auto-save-target="timestamp">
            Last saved: <span data-auto-save-target="time">--</span>
          </div>
        </div>
      </div>
      
      <!-- Progress Indicator -->
      <div class="bg-gradient-to-r from-indigo-500 to-blue-600 px-8 py-6">
        <div data-controller="data-source-wizard" 
             data-data-source-wizard-total-steps-value="4"
             class="space-y-4">
          
          <!-- Step Info -->
          <div class="flex items-center justify-between text-white">
            <div>
              <h2 class="text-xl font-semibold" data-data-source-wizard-target="stepTitle">Choose Your Platform</h2>
              <p class="text-indigo-100 text-sm" data-data-source-wizard-target="stepDescription">Select the platform you want to connect</p>
            </div>
            <div class="text-right">
              <div class="text-sm font-medium">
                Step <span data-data-source-wizard-target="currentStep">1</span> of 4
              </div>
              <div class="text-xs text-indigo-100" data-data-source-wizard-target="progressPercent">25% Complete</div>
            </div>
          </div>
          
          <!-- Progress Bar -->
          <div class="w-full bg-indigo-400 bg-opacity-30 rounded-full h-2">
            <div class="bg-white h-2 rounded-full transition-all duration-500 ease-out" 
                 data-data-source-wizard-target="progressBar" 
                 style="width: 25%"></div>
          </div>
          
          <!-- Step Indicators -->
          <div class="flex justify-between items-center text-xs">
            <div class="flex items-center text-white">
              <div class="w-6 h-6 bg-white bg-opacity-30 rounded-full flex items-center justify-center mr-2">
                <span class="font-semibold text-indigo-600">1</span>
              </div>
              Platform
            </div>
            <div class="flex items-center text-indigo-200">
              <div class="w-6 h-6 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-2">
                <span class="font-semibold">2</span>
              </div>
              Configure
            </div>
            <div class="flex items-center text-indigo-200">
              <div class="w-6 h-6 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-2">
                <span class="font-semibold">3</span>
              </div>
              Preview
            </div>
            <div class="flex items-center text-indigo-200">
              <div class="w-6 h-6 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-2">
                <span class="font-semibold">4</span>
              </div>
              Launch
            </div>
          </div>
        </div>
      </div>

      <!-- Enhanced Form Container -->
      <%= form_with model: @data_source, local: true, 
                    data: { 
                      data_source_wizard_target: "form",
                      auto_save_target: "form",
                      turbo: false 
                    }, 
                    class: "h-full",
                    html: { 
                      'aria-label': 'Data Source Configuration Wizard',
                      role: 'form' 
                    } do |form| %>
        
        <!-- Step 1: Platform Selection -->
        <div class="wizard-step" data-data-source-wizard-target="step1" data-step="1" role="tabpanel" aria-labelledby="step1-tab">
          <%= render 'platform_selection', 
                data_source_configs: data_source_configs.reject { |k, v| v[:status] == 'coming_soon' },
                form: form %>
          
          <!-- Coming Soon Platforms -->
          <%= render 'coming_soon_platforms', 
                data_source_configs: data_source_configs.select { |k, v| v[:status] == 'coming_soon' } %>
        </div>

        <!-- Step 2: Configuration -->
        <div class="wizard-step hidden" data-data-source-wizard-target="step2" data-step="2" role="tabpanel" aria-labelledby="step2-tab">
          <%= render 'configuration_step', 
                form: form,
                sync_frequencies: sync_frequencies %>
        </div>

        <!-- Step 3: Data Preview -->
        <div class="wizard-step hidden" data-data-source-wizard-target="step3" data-step="3" role="tabpanel" aria-labelledby="step3-tab">
          <%= render 'data_preview_step', 
                file_config: file_config %>
        </div>

        <!-- Step 4: Final Setup -->
        <div class="wizard-step hidden" data-data-source-wizard-target="step4" data-step="4" role="tabpanel" aria-labelledby="step4-tab">
          <%= render 'final_setup_step', 
                form: form %>
        </div>

        <!-- Enhanced Navigation Footer -->
        <%= render 'wizard_navigation' %>

      <% end %>
    </div>

    <!-- Additional Help Section -->
    <div class="mt-8 grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- Setup Guide -->
      <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
          <svg class="w-5 h-5 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
          </svg>
          Integration Guides
        </h3>
        <div class="space-y-3">
          <% ['Shopify', 'QuickBooks', 'Stripe'].each do |platform| %>
            <a href="#" class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
              <span class="text-sm font-medium text-gray-900 dark:text-white">How to connect <%= platform %></span>
              <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
              </svg>
            </a>
          <% end %>
        </div>
      </div>

      <!-- Support -->
      <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
          <svg class="w-5 h-5 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          Need Help?
        </h3>
        <div class="space-y-4">
          <p class="text-sm text-gray-600 dark:text-gray-400">
            Our support team is here to help you get connected quickly and securely.
          </p>
          <div class="flex space-x-3">
            <a href="mailto:<EMAIL>" 
               class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors">
              Email Support
            </a>
            <a href="#" 
               class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 text-sm font-medium rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
              Live Chat
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- Error Display -->
    <% if @data_source.errors.any? %>
      <div class="mt-8 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-2xl p-6">
        <div class="flex items-start">
          <svg class="w-6 h-6 text-red-400 mr-3 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z"></path>
          </svg>
          <div>
            <h3 class="text-lg font-semibold text-red-800 dark:text-red-200 mb-2">Please fix the following errors:</h3>
            <ul class="list-disc list-inside space-y-1 text-red-700 dark:text-red-300">
              <% @data_source.errors.full_messages.each do |message| %>
                <li><%= message %></li>
              <% end %>
            </ul>
          </div>
        </div>
      </div>
    <% end %>
  </div>
</div>
