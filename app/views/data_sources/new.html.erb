<% content_for :page_title, "Connect Data Source" %>

<%
  # Initialize data for the wizard
  @wizard_data = DataSourceWizardService.new.prepare_wizard_data
  data_source_configs = @wizard_data[:configurations]
  sync_frequencies = @wizard_data[:sync_frequencies]
  file_config = @wizard_data[:file_config]
%>

<div class="min-h-screen bg-gray-50">
  <!-- Professional Header -->
  <div class="bg-white border-b border-gray-200 shadow-sm">
    <div class="px-4 sm:px-6 lg:px-8 py-6">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <div class="flex items-center space-x-3">
            <div class="p-2 bg-indigo-600 rounded-lg shadow-sm">
              <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7h-4l-2-3H6a2 2 0 00-2 2v3M4 7v10a2 2 0 002 2h12a2 2 0 002-2V9a2 2 0 00-2-2h-1" />
              </svg>
            </div>
            <h1 class="text-2xl font-semibold text-gray-900">
              Connect Data Source
            </h1>
          </div>
          <p class="mt-1 text-sm text-gray-600">Seamlessly integrate your business data for powerful insights</p>
        </div>
        <div class="mt-4 sm:mt-0">
          <%= link_to data_sources_path, class: "inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 shadow-sm transition-colors duration-200" do %>
            <svg class="h-4 w-4 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 15L3 9m0 0l6-6M3 9h12a6 6 0 010 12h-3" />
            </svg>
            Back to Sources
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8" 
       data-controller="data-source-wizard auto-save" 
       data-auto-save-url-value="<%= auto_save_data_sources_path %>">
    
    <!-- Professional Main Card Container -->
    <div class="bg-white shadow-sm rounded-lg border border-gray-200 overflow-hidden">
      <!-- Auto-save Status -->
      <div class="px-6 py-3 bg-green-50 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div class="text-sm font-medium text-gray-700" data-auto-save-target="status">
            <span class="inline-flex items-center">
              <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
              All changes saved automatically
            </span>
          </div>
          <div class="text-xs font-medium text-gray-600" data-auto-save-target="timestamp">
            Last saved: <span data-auto-save-target="time" class="font-medium">--</span>
          </div>
        </div>
      </div>
      
        <!-- Professional Progress Indicator -->
        <div class="bg-indigo-600 px-6 py-6">
          <div data-controller="data-source-wizard" 
               data-data-source-wizard-total-steps-value="4"
               class="space-y-4">
            
            <!-- Step Info -->
            <div class="flex items-center justify-between text-white">
              <div>
                <h2 class="text-xl font-semibold" data-data-source-wizard-target="stepTitle">Choose Your Platform</h2>
                <p class="text-indigo-100 text-sm mt-1" data-data-source-wizard-target="stepDescription">Select the platform you want to connect</p>
              </div>
              <div class="text-right">
                <div class="text-lg font-medium">
                  Step <span data-data-source-wizard-target="currentStep" class="text-xl font-semibold">1</span> of 4
                </div>
                <div class="text-sm text-indigo-100" data-data-source-wizard-target="progressPercent">25% Complete</div>
              </div>
            </div>
            
            <!-- Professional Progress Bar -->
            <div class="relative">
              <div class="w-full bg-indigo-700 rounded-full h-2 overflow-hidden">
                <div class="h-full bg-white rounded-full transition-all duration-500 ease-out" 
                     data-data-source-wizard-target="progressBar" 
                     style="width: 25%"></div>
              </div>
            </div>
            
            <!-- Professional Step Indicators -->
            <div class="flex justify-between items-center">
              <div class="flex items-center">
                <div class="w-8 h-8 bg-white rounded-full flex items-center justify-center mr-3">
                  <span class="font-medium text-indigo-600">1</span>
                </div>
                <span class="text-white font-medium text-sm">Platform</span>
              </div>
              <div class="flex items-center">
                <div class="w-8 h-8 bg-indigo-700 rounded-full flex items-center justify-center mr-3">
                  <span class="font-medium text-indigo-200">2</span>
                </div>
                <span class="text-indigo-100 text-sm">Configure</span>
              </div>
              <div class="flex items-center">
                <div class="w-8 h-8 bg-indigo-700 rounded-full flex items-center justify-center mr-3">
                  <span class="font-medium text-indigo-200">3</span>
                </div>
                <span class="text-indigo-100 text-sm">Preview</span>
              </div>
              <div class="flex items-center">
                <div class="w-8 h-8 bg-indigo-700 rounded-full flex items-center justify-center mr-3">
                  <span class="font-medium text-indigo-200">4</span>
                </div>
                <span class="text-indigo-100 text-sm">Launch</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Enhanced Form Container -->
        <%= form_with model: @data_source, local: true, 
                      data: { 
                        data_source_wizard_target: "form",
                        auto_save_target: "form",
                        turbo: false 
                      }, 
                      class: "h-full relative",
                      html: { 
                        'aria-label': 'Data Source Configuration Wizard',
                        role: 'form' 
                      } do |form| %>
        
          <!-- Step 1: Platform Selection -->
          <div class="wizard-step p-8" data-data-source-wizard-target="step1" data-step="1" role="tabpanel" aria-labelledby="step1-tab">
            <%= render 'platform_selection', 
                  data_source_configs: data_source_configs.reject { |k, v| v[:status] == 'coming_soon' },
                  form: form %>
            
            <!-- Coming Soon Platforms -->
            <%= render 'coming_soon_platforms', 
                  data_source_configs: data_source_configs.select { |k, v| v[:status] == 'coming_soon' } %>
          </div>

          <!-- Step 2: Configuration -->
          <div class="wizard-step hidden p-8" data-data-source-wizard-target="step2" data-step="2" role="tabpanel" aria-labelledby="step2-tab">
            <%= render 'configuration_step', 
                  form: form,
                  sync_frequencies: sync_frequencies %>
          </div>

          <!-- Step 3: Data Preview -->
          <div class="wizard-step hidden p-8" data-data-source-wizard-target="step3" data-step="3" role="tabpanel" aria-labelledby="step3-tab">
            <%= render 'data_preview_step', 
                  file_config: file_config %>
          </div>

          <!-- Step 4: Final Setup -->
          <div class="wizard-step hidden p-8" data-data-source-wizard-target="step4" data-step="4" role="tabpanel" aria-labelledby="step4-tab">
            <%= render 'final_setup_step', 
                  form: form %>
          </div>

          <!-- Enhanced Navigation Footer -->
          <%= render 'wizard_navigation' %>

        <% end %>
      </div>
    </div>

    <!-- Professional Help Section -->
    <div class="mt-8 grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Setup Guide -->
      <div class="bg-white shadow-sm rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
        <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
          <div class="p-2 bg-blue-600 rounded-lg shadow-sm mr-3">
            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
            </svg>
          </div>
          Integration Guides
        </h3>
        <div class="space-y-3">
          <% ['Shopify', 'QuickBooks', 'Stripe'].each do |platform| %>
            <a href="#" class="flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-200 hover:bg-gray-100 transition-colors duration-200">
              <span class="text-sm font-medium text-gray-900">How to connect <%= platform %></span>
              <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
              </svg>
            </a>
          <% end %>
        </div>
      </div>

      <!-- Support -->
      <div class="bg-white shadow-sm rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
        <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
          <div class="p-2 bg-green-600 rounded-lg shadow-sm mr-3">
            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          Need Help?
        </h3>
        <div class="space-y-4">
          <p class="text-sm text-gray-600">
            Our support team is here to help you get connected quickly and securely.
          </p>
          <div class="flex space-x-3">
            <a href="mailto:<EMAIL>" 
               class="inline-flex items-center px-4 py-2 bg-indigo-600 text-white rounded-lg font-medium shadow-sm hover:bg-indigo-700 transition-colors duration-200">
              <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              Email Support
            </a>
            <a href="#" 
               class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 shadow-sm transition-colors duration-200">
              <svg class="h-4 w-4 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
              Live Chat
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- Error Display -->
    <% if @data_source.errors.any? %>
      <div class="mt-8 bg-white shadow-sm rounded-lg border border-red-200 p-6">
        <div class="flex items-start">
          <div class="p-2 bg-red-600 rounded-lg shadow-sm mr-4">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
          </div>
          <div>
            <h3 class="text-lg font-medium text-red-900 mb-2">Please fix the following errors:</h3>
            <ul class="list-disc list-inside space-y-1 text-red-700">
              <% @data_source.errors.full_messages.each do |message| %>
                <li><%= message %></li>
              <% end %>
            </ul>
          </div>
        </div>
      </div>
    <% end %>
  </div>
  
</div>
