<!-- DataFlow Pro Layout -->
<div class="min-h-screen bg-df-background theme-transition" data-controller="dataflow-navigation theme-toggle">
  
  <!-- Sidebar Navigation -->
  <nav class="sidebar" id="sidebar" data-dataflow-navigation-target="sidebar">
    <div class="sidebar-header">
      <h2>DataFlow Pro</h2>
      <button class="sidebar-toggle" data-action="click->dataflow-navigation#toggleSidebar">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
        </svg>
      </button>
    </div>
    
    <ul class="nav-menu">
      <li class="nav-item active" data-section="dashboard" data-action="click->dataflow-navigation#switchSection">
        <span class="nav-icon">📊</span>
        <span class="nav-text">Dashboard</span>
      </li>
      <li class="nav-item" data-section="predictive" data-action="click->dataflow-navigation#switchSection">
        <span class="nav-icon">🔮</span>
        <span class="nav-text">Predictive Analytics</span>
      </li>
      <li class="nav-item" data-section="builder" data-action="click->dataflow-navigation#switchSection">
        <span class="nav-icon">🛠️</span>
        <span class="nav-text">Analytics Builder</span>
      </li>
      <li class="nav-item" data-section="etl" data-action="click->dataflow-navigation#switchSection">
        <span class="nav-icon">🔄</span>
        <span class="nav-text">ETL Pipelines</span>
      </li>
      <li class="nav-item" data-section="templates" data-action="click->dataflow-navigation#switchSection">
        <span class="nav-icon">📋</span>
        <span class="nav-text">Industry Templates</span>
      </li>
      <li class="nav-item" data-section="marketplace" data-action="click->dataflow-navigation#switchSection">
        <span class="nav-icon">🛍️</span>
        <span class="nav-text">Integration Marketplace</span>
      </li>
      <li class="nav-item" data-section="collaboration" data-action="click->dataflow-navigation#switchSection">
        <span class="nav-icon">👥</span>
        <span class="nav-text">Team Collaboration</span>
      </li>
      <li class="nav-item" data-section="mobile" data-action="click->dataflow-navigation#switchSection">
        <span class="nav-icon">📱</span>
        <span class="nav-text">Mobile Dashboard</span>
      </li>
      <li class="nav-item" data-section="partner" data-action="click->dataflow-navigation#switchSection">
        <span class="nav-icon">🏢</span>
        <span class="nav-text">Partner Portal</span>
      </li>
      <li class="nav-item" data-section="costs" data-action="click->dataflow-navigation#switchSection">
        <span class="nav-icon">💰</span>
        <span class="nav-text">Cost Optimization</span>
      </li>
      <li class="nav-item" data-section="security" data-action="click->dataflow-navigation#switchSection">
        <span class="nav-icon">🔒</span>
        <span class="nav-text">Security & Compliance</span>
      </li>
    </ul>
  </nav>
  
  <!-- Main Content Area -->
  <main class="main-content" id="mainContent">
    <!-- Header -->
    <header class="header">
      <div class="header-left">
        <h1 id="pageTitle" data-dataflow-navigation-target="pageTitle">Executive Dashboard</h1>
        <p id="pageSubtitle" data-dataflow-navigation-target="pageSubtitle">Real-time insights and AI-powered analytics for your business</p>
      </div>
      <div class="header-right">
        <button class="btn btn--secondary" data-controller="ai-assistant" data-action="click->ai-assistant#open">
          <span class="mr-2">🤖</span> AI Assistant
        </button>
        <button class="btn btn--outline" data-action="click->theme-toggle#toggle">
          <span data-theme-toggle-target="icon">🌙</span>
        </button>
        <div class="user-profile relative" data-controller="dropdown">
          <button data-action="click->dropdown#toggle" class="flex items-center">
            <% if current_user.avatar.attached? %>
              <%= image_tag current_user.avatar, alt: current_user.full_name, class: "w-8 h-8 rounded-full" %>
            <% else %>
              <div class="w-8 h-8 rounded-full bg-df-primary flex items-center justify-center text-white font-semibold">
                <%= current_user.initials %>
              </div>
            <% end %>
          </button>
          <div data-dropdown-target="menu" class="hidden absolute right-0 mt-2 w-48 bg-df-surface rounded-df-md shadow-df-lg border border-df-border z-50">
            <%= link_to "Profile", edit_user_registration_path, class: "block px-4 py-2 text-df-text hover:bg-df-secondary" %>
            <%= link_to "Settings", settings_path, class: "block px-4 py-2 text-df-text hover:bg-df-secondary" %>
            <hr class="border-df-border">
            <%= button_to "Sign Out", destroy_user_session_path, method: :delete, class: "block w-full text-left px-4 py-2 text-df-text hover:bg-df-secondary" %>
          </div>
        </div>
      </div>
    </header>
    
    <!-- Flash Messages -->
    <%= render 'shared/flash_messages' %>
    
    <!-- Content Sections -->
    <div data-dataflow-navigation-target="content">
      <%= yield %>
    </div>
  </main>
  
  <!-- AI Assistant Modal -->
  <div class="modal" data-ai-assistant-target="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h3 class="text-df-xl font-semibold flex items-center">
          <span class="mr-2">🤖</span> AI Assistant
        </h3>
        <button class="modal-close" data-action="click->ai-assistant#close">&times;</button>
      </div>
      <div class="modal-body p-df-24">
        <div class="chat-container h-96 overflow-y-auto mb-df-16 p-df-16 bg-df-background rounded-df-md" data-ai-assistant-target="chatContainer">
          <div class="chat-message ai bg-df-secondary p-df-12 rounded-df-md mb-df-8">
            <p>Hello! I'm your AI assistant. I can help you analyze data, create reports, and optimize your business operations. What would you like to know?</p>
          </div>
        </div>
        <div class="chat-input flex gap-df-8">
          <input type="text" 
                 class="form-control flex-1" 
                 placeholder="Ask me anything about your data..." 
                 data-ai-assistant-target="input"
                 data-action="keypress->ai-assistant#handleKeypress">
          <button class="btn btn--primary" data-action="click->ai-assistant#sendMessage">Send</button>
        </div>
      </div>
    </div>
  </div>
</div>