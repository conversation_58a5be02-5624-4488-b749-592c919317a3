<% content_for :title, "Billing & Invoices" %>

<div class="min-h-screen bg-gradient-to-br from-gray-50 to-white">
  <!-- Hero Section -->
  <div class="bg-gradient-to-r from-emerald-600 via-blue-600 to-indigo-600 shadow-2xl">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-6">
          <%= link_to organization_path, class: "h-12 w-12 bg-white/20 rounded-2xl flex items-center justify-center backdrop-blur-sm hover:bg-white/30 transition-all duration-200 group" do %>
            <svg class="h-6 w-6 text-white group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
          <% end %>
          <div class="w-20 h-20 bg-white/20 rounded-3xl flex items-center justify-center backdrop-blur-sm">
            <svg class="h-10 w-10 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 002.25-2.25V6.75A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25v10.5A2.25 2.25 0 004.5 19.5z" />
            </svg>
          </div>
          <div>
            <h1 class="text-4xl font-bold text-white mb-2">Billing & Invoices</h1>
            <p class="text-emerald-100 text-lg">Manage your subscription, payment methods, and billing history</p>
          </div>
        </div>
        
        <div class="flex gap-4">
          <button class="inline-flex items-center gap-3 bg-white/20 hover:bg-white/30 text-white font-semibold py-4 px-6 rounded-2xl transition-all duration-200 backdrop-blur-sm border border-white/20 hover:border-white/30 transform hover:scale-105">
            <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3" />
            </svg>
            Download Invoice
          </button>
        </div>
      </div>
    </div>
  </div>

  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

    <!-- Current Plan Overview -->
    <div class="bg-gradient-to-br from-white to-gray-50/50 rounded-3xl shadow-2xl border border-gray-100/50 overflow-hidden">
      <div class="bg-gradient-to-r from-emerald-50 to-blue-50 p-8 border-b border-gray-100">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-4">
            <div class="w-16 h-16 bg-gradient-to-r from-emerald-500 to-blue-600 rounded-2xl flex items-center justify-center">
              <svg class="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 01-1.043 3.296 3.745 3.745 0 01-3.296 1.043A3.745 3.745 0 0112 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 01-3.296-1.043 3.745 3.745 0 01-1.043-3.296A3.745 3.745 0 013 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 011.043-3.296 3.746 3.746 0 013.296-1.043A3.746 3.746 0 0112 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 013.296 1.043 3.746 3.746 0 011.043 3.296A3.745 3.745 0 0121 12z" />
              </svg>
            </div>
            <div>
              <h3 class="text-2xl font-bold text-gray-900 mb-1">Current Plan</h3>
              <p class="text-gray-600">Your active subscription details</p>
            </div>
          </div>
        </div>
      </div>
      
      <div class="p-8">
        <div class="flex items-center justify-between mb-8">
          <div>
            <h4 class="text-3xl font-bold text-gray-900 mb-2"><%= @organization.plan.humanize %> Plan</h4>
            <div class="flex items-center gap-3">
              <span class="text-sm font-medium text-gray-600">Status:</span>
              <span class="inline-flex items-center gap-2 px-4 py-2 rounded-full text-sm font-semibold <%= @organization.active? ? 'bg-gradient-to-r from-emerald-100 to-green-100 text-emerald-700 border border-emerald-200' : 'bg-gradient-to-r from-yellow-100 to-orange-100 text-yellow-700 border border-yellow-200' %>">
                <div class="w-2 h-2 <%= @organization.active? ? 'bg-emerald-500' : 'bg-yellow-500' %> rounded-full animate-pulse"></div>
                <%= @organization.status.humanize %>
              </span>
            </div>
          </div>
          <div class="text-right">
            <% unless @organization.free_trial_plan? %>
              <div class="bg-gradient-to-r from-indigo-50 to-blue-50 rounded-2xl p-6 border border-indigo-200">
                <p class="text-4xl font-bold bg-gradient-to-r from-indigo-600 to-blue-600 bg-clip-text text-transparent mb-1">
                  $<%= case @organization.plan
                        when 'starter' then '49'
                        when 'growth' then '149' 
                        when 'scale' then '399'
                        when 'enterprise' then 'Custom'
                        else '0'
                      end %>
                </p>
                <p class="text-sm font-medium text-gray-600">per month</p>
              </div>
            <% else %>
              <div class="bg-gradient-to-r from-emerald-50 to-green-50 rounded-2xl p-6 border border-emerald-200">
                <p class="text-4xl font-bold bg-gradient-to-r from-emerald-600 to-green-600 bg-clip-text text-transparent mb-1">Free Trial</p>
                <p class="text-sm font-medium text-gray-600">14 days remaining</p>
              </div>
            <% end %>
          </div>
        </div>

        <div class="grid grid-cols-1 gap-6 sm:grid-cols-3">
          <div class="bg-gradient-to-br from-blue-50 to-indigo-50 p-6 rounded-2xl border border-blue-200 hover:shadow-lg transition-all duration-300">
            <div class="flex items-center justify-between mb-4">
              <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center">
                <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M20.25 6.375c0 2.278-3.694 4.125-8.25 4.125S3.75 8.653 3.75 6.375m16.5 0c0-2.278-3.694-4.125-8.25-4.125S3.75 4.097 3.75 6.375m16.5 0v11.25c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125V6.375m16.5 0v3.75m-16.5-3.75v3.75m16.5 0v3.75C20.25 16.153 16.556 18 12 18s-8.25-1.847-8.25-4.125v-3.75m16.5 0c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125" />
                </svg>
              </div>
              <span class="text-xs font-semibold px-3 py-1 bg-blue-100 text-blue-700 rounded-full">Records</span>
            </div>
            <h3 class="text-lg font-bold text-gray-900 mb-2">Data Records</h3>
            <p class="text-sm text-gray-600 mt-1">
              <%= @organization.monthly_data_limit == Float::INFINITY ? "Unlimited" : number_with_delimiter(@organization.monthly_data_limit) %> per month
            </p>
          </div>
          
          <div class="bg-gradient-to-br from-emerald-50 to-green-50 p-6 rounded-2xl border border-emerald-200 hover:shadow-lg transition-all duration-300">
            <div class="flex items-center justify-between mb-4">
              <div class="w-12 h-12 bg-gradient-to-r from-emerald-500 to-green-600 rounded-xl flex items-center justify-center">
                <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z" />
                </svg>
              </div>
              <span class="text-xs font-semibold px-3 py-1 bg-emerald-100 text-emerald-700 rounded-full">API</span>
            </div>
            <h3 class="text-lg font-bold text-gray-900 mb-2">API Requests</h3>
            <p class="text-sm text-gray-600 mt-1">
              <%= @organization.monthly_api_requests_limit == Float::INFINITY ? "Unlimited" : number_with_delimiter(@organization.monthly_api_requests_limit) %> per month
            </p>
          </div>
          
          <div class="bg-gradient-to-br from-purple-50 to-violet-50 p-6 rounded-2xl border border-purple-200 hover:shadow-lg transition-all duration-300">
            <div class="flex items-center justify-between mb-4">
              <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-violet-600 rounded-xl flex items-center justify-center">
                <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M15 19.128a9.38 9.38 0 002.625.372 9.337 9.337 0 004.121-.952 4.125 4.125 0 00-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 018.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0111.964-3.07M12 6.375a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zm8.25 2.25a2.625 2.625 0 11-5.25 0 2.625 2.625 0 015.25 0z" />
                </svg>
              </div>
              <span class="text-xs font-semibold px-3 py-1 bg-purple-100 text-purple-700 rounded-full">Team</span>
            </div>
            <h3 class="text-lg font-bold text-gray-900 mb-2">Team Members</h3>
            <p class="text-sm text-gray-600 mt-1">
              Up to <%= @organization.max_users == Float::INFINITY ? "unlimited" : @organization.max_users %> users
            </p>
          </div>
        </div>

        <% unless @organization.enterprise_plan? %>
          <div class="mt-6 flex gap-3">
            <button type="button" class="rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
              Upgrade Plan
            </button>
            <% unless @organization.free_trial_plan? %>
              <button type="button" class="rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50">
                Change Plan
              </button>
            <% end %>
          </div>
        <% end %>
      </div>
    </div>
  </div>

  <!-- Payment Method -->
  <div class="mt-8">
    <div class="bg-gradient-to-br from-white to-gray-50/50 rounded-3xl shadow-2xl border border-gray-100/50 overflow-hidden">
      <div class="bg-gradient-to-r from-violet-50 to-purple-50 p-8 border-b border-gray-100">
        <div class="flex items-center gap-4">
          <div class="w-16 h-16 bg-gradient-to-r from-violet-500 to-purple-600 rounded-2xl flex items-center justify-center">
            <svg class="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 002.25-2.25V6.75A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25v10.5A2.25 2.25 0 004.5 19.5z" />
            </svg>
          </div>
          <div>
            <h3 class="text-2xl font-bold text-gray-900 mb-1">Payment Method</h3>
            <p class="text-gray-600">Manage your billing information</p>
          </div>
        </div>
      </div>
      
      <div class="p-8">
        <% if @organization.stripe_customer_id.present? %>
          <div class="bg-gradient-to-r from-gray-50 to-gray-100/50 rounded-2xl p-6 border border-gray-200">
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-4">
                <div class="w-14 h-14 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center">
                  <svg class="h-7 w-7 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 002.25-2.25V6.75A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25v10.5A2.25 2.25 0 004.5 19.5z" />
                  </svg>
                </div>
                <div>
                  <p class="text-xl font-bold text-gray-900 mb-1">•••• •••• •••• 4242</p>
                  <p class="text-sm text-gray-600">Expires 12/24</p>
                  <span class="inline-flex items-center gap-2 px-3 py-1 mt-2 rounded-full text-xs font-semibold bg-emerald-100 text-emerald-700 border border-emerald-200">
                    <div class="w-2 h-2 bg-emerald-500 rounded-full"></div>
                    Active
                  </span>
                </div>
              </div>
              <button type="button" class="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-indigo-600 to-blue-600 text-white font-semibold rounded-xl hover:from-indigo-700 hover:to-blue-700 transition-all duration-200 shadow-lg hover:shadow-xl">
                <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10" />
                </svg>
                Update
              </button>
            </div>
          </div>
        <% else %>
          <div class="text-center py-12">
            <div class="w-20 h-20 bg-gradient-to-r from-gray-100 to-gray-200 rounded-3xl flex items-center justify-center mx-auto mb-6">
              <svg class="h-10 w-10 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 002.25-2.25V6.75A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25v10.5A2.25 2.25 0 004.5 19.5z" />
              </svg>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-2">No payment method added</h3>
            <p class="text-gray-600 mb-8 max-w-md mx-auto">Add a secure payment method to upgrade your plan and unlock premium features.</p>
            <button type="button" class="inline-flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-indigo-600 to-blue-600 text-white font-semibold rounded-2xl hover:from-indigo-700 hover:to-blue-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
              <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path d="M10.75 4.75a.75.75 0 00-1.5 0v4.5h-4.5a.75.75 0 000 1.5h4.5v4.5a.75.75 0 001.5 0v-4.5h4.5a.75.75 0 000-1.5h-4.5v-4.5z" />
              </svg>
              Add Payment Method
            </button>
          </div>
        <% end %>
      </div>
    </div>
  </div>

  <!-- Billing History -->
  <div class="mt-8">
    <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-base font-semibold leading-6 text-gray-900">Billing History</h3>
      </div>
      <div class="px-6 py-6">
        <!-- Placeholder for invoices -->
        <div class="text-center py-8">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
          </svg>
          <h3 class="mt-2 text-sm font-semibold text-gray-900">No invoices yet</h3>
          <p class="mt-1 text-sm text-gray-500">Your billing history will appear here once you have invoices.</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Plan Comparison (if not enterprise) -->
  <% unless @organization.enterprise_plan? %>
    <div class="mt-8">
      <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-base font-semibold leading-6 text-gray-900">Available Plans</h3>
        </div>
        <div class="px-6 py-6">
          <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
            
            <!-- Free Trial -->
            <div class="relative rounded-lg border border-gray-300 bg-white p-6 shadow-sm <%= @organization.free_trial_plan? ? 'ring-2 ring-indigo-600' : '' %>">
              <div class="text-center">
                <h3 class="text-lg font-semibold text-gray-900">Free Trial</h3>
                <p class="mt-2 text-3xl font-bold tracking-tight text-gray-900">$0</p>
                <p class="text-sm text-gray-500">14 days</p>
              </div>
              <ul class="mt-6 space-y-3 text-sm text-gray-600">
                <li class="flex gap-3">
                  <svg class="h-5 w-5 flex-none text-green-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clip-rule="evenodd" />
                  </svg>
                  10,000 records/month
                </li>
                <li class="flex gap-3">
                  <svg class="h-5 w-5 flex-none text-green-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clip-rule="evenodd" />
                  </svg>
                  2 team members
                </li>
                <li class="flex gap-3">
                  <svg class="h-5 w-5 flex-none text-green-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clip-rule="evenodd" />
                  </svg>
                  2 data sources
                </li>
              </ul>
              <% if @organization.free_trial_plan? %>
                <div class="mt-6 block w-full rounded-md bg-indigo-600 px-3 py-2 text-center text-sm font-semibold text-white">
                  Current Plan
                </div>
              <% end %>
            </div>

            <!-- Starter -->
            <div class="relative rounded-lg border border-gray-300 bg-white p-6 shadow-sm <%= @organization.starter_plan? ? 'ring-2 ring-indigo-600' : '' %>">
              <div class="text-center">
                <h3 class="text-lg font-semibold text-gray-900">Starter</h3>
                <p class="mt-2 text-3xl font-bold tracking-tight text-gray-900">$49</p>
                <p class="text-sm text-gray-500">per month</p>
              </div>
              <ul class="mt-6 space-y-3 text-sm text-gray-600">
                <li class="flex gap-3">
                  <svg class="h-5 w-5 flex-none text-green-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clip-rule="evenodd" />
                  </svg>
                  100,000 records/month
                </li>
                <li class="flex gap-3">
                  <svg class="h-5 w-5 flex-none text-green-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clip-rule="evenodd" />
                  </svg>
                  5 team members
                </li>
                <li class="flex gap-3">
                  <svg class="h-5 w-5 flex-none text-green-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clip-rule="evenodd" />
                  </svg>
                  5 data sources
                </li>
              </ul>
              <% if @organization.starter_plan? %>
                <div class="mt-6 block w-full rounded-md bg-indigo-600 px-3 py-2 text-center text-sm font-semibold text-white">
                  Current Plan
                </div>
              <% else %>
                <button type="button" class="mt-6 block w-full rounded-md bg-indigo-600 px-3 py-2 text-center text-sm font-semibold text-white hover:bg-indigo-500">
                  Upgrade to Starter
                </button>
              <% end %>
            </div>

            <!-- Growth -->
            <div class="relative rounded-lg border border-gray-300 bg-white p-6 shadow-sm <%= @organization.growth_plan? ? 'ring-2 ring-indigo-600' : '' %>">
              <div class="text-center">
                <h3 class="text-lg font-semibold text-gray-900">Growth</h3>
                <p class="mt-2 text-3xl font-bold tracking-tight text-gray-900">$149</p>
                <p class="text-sm text-gray-500">per month</p>
              </div>
              <ul class="mt-6 space-y-3 text-sm text-gray-600">
                <li class="flex gap-3">
                  <svg class="h-5 w-5 flex-none text-green-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clip-rule="evenodd" />
                  </svg>
                  500,000 records/month
                </li>
                <li class="flex gap-3">
                  <svg class="h-5 w-5 flex-none text-green-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clip-rule="evenodd" />
                  </svg>
                  20 team members
                </li>
                <li class="flex gap-3">
                  <svg class="h-5 w-5 flex-none text-green-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clip-rule="evenodd" />
                  </svg>
                  15 data sources
                </li>
              </ul>
              <% if @organization.growth_plan? %>
                <div class="mt-6 block w-full rounded-md bg-indigo-600 px-3 py-2 text-center text-sm font-semibold text-white">
                  Current Plan
                </div>
              <% else %>
                <button type="button" class="mt-6 block w-full rounded-md bg-indigo-600 px-3 py-2 text-center text-sm font-semibold text-white hover:bg-indigo-500">
                  Upgrade to Growth
                </button>
              <% end %>
            </div>

            <!-- Scale -->
            <div class="relative rounded-lg border border-gray-300 bg-white p-6 shadow-sm <%= @organization.scale_plan? ? 'ring-2 ring-indigo-600' : '' %>">
              <div class="text-center">
                <h3 class="text-lg font-semibold text-gray-900">Scale</h3>
                <p class="mt-2 text-3xl font-bold tracking-tight text-gray-900">$399</p>
                <p class="text-sm text-gray-500">per month</p>
              </div>
              <ul class="mt-6 space-y-3 text-sm text-gray-600">
                <li class="flex gap-3">
                  <svg class="h-5 w-5 flex-none text-green-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clip-rule="evenodd" />
                  </svg>
                  2M records/month
                </li>
                <li class="flex gap-3">
                  <svg class="h-5 w-5 flex-none text-green-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clip-rule="evenodd" />
                  </svg>
                  100 team members
                </li>
                <li class="flex gap-3">
                  <svg class="h-5 w-5 flex-none text-green-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clip-rule="evenodd" />
                  </svg>
                  50 data sources
                </li>
              </ul>
              <% if @organization.scale_plan? %>
                <div class="mt-6 block w-full rounded-md bg-indigo-600 px-3 py-2 text-center text-sm font-semibold text-white">
                  Current Plan
                </div>
              <% else %>
                <button type="button" class="mt-6 block w-full rounded-md bg-indigo-600 px-3 py-2 text-center text-sm font-semibold text-white hover:bg-indigo-500">
                  Upgrade to Scale
                </button>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </div>
  <% end %>
</div>