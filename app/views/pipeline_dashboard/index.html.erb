<%= render 'shared/page_header',
  title: 'Pipeline Dashboard',
  description: 'Monitor and manage your data processing pipelines',
  actions: [
    { text: 'New Pipeline', path: new_data_source_path, class: 'btn-primary' }
  ]
%>

<!-- Pipeline Statistics Grid -->
<div class="grid grid-cols-1 lg:grid-cols-6 gap-6 mb-8">
  <!-- Total Pipelines -->
  <div class="bg-white rounded-lg shadow p-6">
    <div class="flex items-center justify-between">
      <div>
        <p class="text-sm text-gray-600">Total Pipelines</p>
        <p class="text-2xl font-bold text-gray-900"><%= @statistics[:total_pipelines] %></p>
      </div>
      <div class="flex items-center justify-center w-12 h-12 bg-gray-100 rounded-lg">
        <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
        </svg>
      </div>
    </div>
  </div>

  <!-- Running Pipelines -->
  <div class="bg-white rounded-lg shadow p-6">
    <div class="flex items-center justify-between">
      <div>
        <p class="text-sm text-gray-600">Running</p>
        <p class="text-2xl font-bold text-blue-600"><%= @statistics[:running_pipelines] %></p>
      </div>
      <div class="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg">
        <svg class="w-6 h-6 text-blue-600 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
        </svg>
      </div>
    </div>
  </div>

  <!-- Successful (24h) -->
  <div class="bg-white rounded-lg shadow p-6">
    <div class="flex items-center justify-between">
      <div>
        <p class="text-sm text-gray-600">Successful (24h)</p>
        <p class="text-2xl font-bold text-green-600"><%= @statistics[:successful_pipelines] %></p>
      </div>
      <div class="flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg">
        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
      </div>
    </div>
  </div>

  <!-- Failed (24h) -->
  <div class="bg-white rounded-lg shadow p-6">
    <div class="flex items-center justify-between">
      <div>
        <p class="text-sm text-gray-600">Failed (24h)</p>
        <p class="text-2xl font-bold text-red-600"><%= @statistics[:failed_pipelines] %></p>
      </div>
      <div class="flex items-center justify-center w-12 h-12 bg-red-100 rounded-lg">
        <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
      </div>
    </div>
  </div>

  <!-- Average Duration -->
  <div class="bg-white rounded-lg shadow p-6">
    <div class="flex items-center justify-between">
      <div>
        <p class="text-sm text-gray-600">Avg Duration</p>
        <p class="text-2xl font-bold text-purple-600"><%= @statistics[:average_duration] %></p>
      </div>
      <div class="flex items-center justify-center w-12 h-12 bg-purple-100 rounded-lg">
        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
      </div>
    </div>
  </div>

  <!-- Success Rate -->
  <div class="bg-white rounded-lg shadow p-6">
    <div class="flex items-center justify-between">
      <div>
        <p class="text-sm text-gray-600">Success Rate</p>
        <p class="text-2xl font-bold text-indigo-600"><%= @statistics[:success_rate] %>%</p>
      </div>
      <div class="flex items-center justify-center w-12 h-12 bg-indigo-100 rounded-lg">
        <svg class="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
        </svg>
      </div>
    </div>
  </div>
</div>

<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
  <!-- Pipeline Performance by Type -->
  <div class="lg:col-span-2 bg-white rounded-lg shadow">
    <div class="p-6 border-b">
      <h3 class="text-lg font-semibold text-gray-900">Pipeline Performance by Type</h3>
    </div>
    <div class="p-6">
      <% if @performance_by_type.any? %>
        <div class="space-y-4">
          <% @performance_by_type.each do |pipeline_name, metrics| %>
            <div class="border rounded-lg p-4">
              <div class="flex items-center justify-between mb-2">
                <h4 class="font-medium text-gray-900"><%= pipeline_name.humanize %></h4>
                <div class="flex items-center gap-4 text-sm">
                  <span class="text-gray-600">Total: <%= metrics[:total] %></span>
                  <span class="text-gray-600">Avg: <%= metrics[:average_duration] %></span>
                </div>
              </div>
              <div class="relative">
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div class="bg-green-600 h-2 rounded-full" style="width: <%= metrics[:success_rate] %>%"></div>
                </div>
                <div class="flex items-center justify-between mt-1">
                  <span class="text-xs text-gray-600">Success Rate</span>
                  <span class="text-xs font-medium text-gray-900"><%= metrics[:success_rate] %>%</span>
                </div>
              </div>
            </div>
          <% end %>
        </div>
      <% else %>
        <p class="text-gray-500 text-center py-8">No pipeline data available yet.</p>
      <% end %>
    </div>
  </div>

  <!-- Active Manual Interventions -->
  <div class="bg-white rounded-lg shadow">
    <div class="p-6 border-b">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-900">Manual Interventions</h3>
        <% if @manual_interventions.any? %>
          <span class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium">
            <%= @manual_interventions.count %> Pending
          </span>
        <% end %>
      </div>
    </div>
    <div class="p-6">
      <% if @manual_interventions.any? %>
        <div class="space-y-3">
          <% @manual_interventions.each do |task| %>
            <div class="border rounded-lg p-3 hover:bg-gray-50 transition-colors">
              <div class="flex items-start justify-between">
                <div class="min-w-0 flex-1">
                  <h4 class="text-sm font-medium text-gray-900 truncate">
                    <%= task.name %>
                  </h4>
                  <p class="text-xs text-gray-600 mt-1">
                    <%= task.pipeline_execution.pipeline_name %>
                  </p>
                  <div class="flex items-center gap-3 mt-2 text-xs">
                    <span class="<%= task.execution_mode_badge_class %> px-2 py-0.5 rounded-full">
                      <%= task.execution_mode.humanize %>
                    </span>
                    <% if task.assignee %>
                      <span class="text-gray-600">Assigned to <%= task.assignee.name %></span>
                    <% else %>
                      <span class="text-amber-600 font-medium">Unassigned</span>
                    <% end %>
                  </div>
                </div>
                <div class="ml-2">
                  <%= link_to manual_task_path(task), class: "text-indigo-600 hover:text-indigo-800" do %>
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                    </svg>
                  <% end %>
                </div>
              </div>
            </div>
          <% end %>
        </div>
        <div class="mt-4">
          <%= link_to "View All Tasks →", manual_tasks_path, 
            class: "text-sm text-indigo-600 hover:text-indigo-800 font-medium" %>
        </div>
      <% else %>
        <p class="text-gray-500 text-center py-4">No manual interventions required.</p>
      <% end %>
    </div>
  </div>
</div>

<!-- Recent Pipeline Executions -->
<div class="mt-8">
  <div class="bg-white rounded-lg shadow">
    <div class="p-6 border-b">
      <h3 class="text-lg font-semibold text-gray-900">Recent Pipeline Executions</h3>
    </div>
    
    <%= turbo_frame_tag "pipeline_executions" do %>
      <% if @pipeline_executions.any? %>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Pipeline
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Data Source
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Progress
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Started
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Duration
                </th>
                <th class="relative px-6 py-3">
                  <span class="sr-only">Actions</span>
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <% @pipeline_executions.each do |execution| %>
                <%= turbo_frame_tag execution do %>
                  <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm font-medium text-gray-900">
                        <%= execution.pipeline_name %>
                      </div>
                      <div class="text-xs text-gray-500">
                        <%= execution.execution_mode.humanize %>
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <% if execution.data_source %>
                        <div class="text-sm text-gray-900">
                          <%= execution.data_source.name %>
                        </div>
                        <div class="text-xs text-gray-500">
                          <%= execution.data_source.source_type.humanize %>
                        </div>
                      <% else %>
                        <span class="text-sm text-gray-500">—</span>
                      <% end %>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span class="<%= execution.status == 'completed' ? 'bg-green-100 text-green-800' : execution.status == 'failed' ? 'bg-red-100 text-red-800' : execution.status == 'running' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800' %> px-2 py-1 text-xs rounded-full font-medium">
                        <%= execution.status.humanize %>
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="flex items-center">
                        <div class="flex-1 bg-gray-200 rounded-full h-2 mr-2">
                          <div class="bg-indigo-600 h-2 rounded-full" style="width: <%= execution.progress || 0 %>%"></div>
                        </div>
                        <span class="text-xs text-gray-600 font-medium"><%= execution.progress || 0 %>%</span>
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <%= execution.started_at.strftime("%b %d, %I:%M %p") %>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <%= execution.duration_formatted || "—" %>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <%= link_to "View Details", pipeline_dashboard_path(execution), 
                        class: "text-indigo-600 hover:text-indigo-900" %>
                    </td>
                  </tr>
                <% end %>
              <% end %>
            </tbody>
          </table>
        </div>
        
        <div class="px-6 py-4 border-t">
          <%= paginate @pipeline_executions %>
        </div>
      <% else %>
        <div class="p-12 text-center">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">No pipelines yet</h3>
          <p class="mt-1 text-sm text-gray-500">Get started by creating a new data source.</p>
          <div class="mt-6">
            <%= link_to "Create Data Source", new_data_source_path, 
              class: "btn btn-primary" %>
          </div>
        </div>
      <% end %>
    <% end %>
  </div>
</div>

<!-- Subscribe to real-time updates -->
<%= turbo_stream_from "pipeline_dashboard" %>