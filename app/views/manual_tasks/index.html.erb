<%= render 'shared/page_header', 
  title: 'Manual Task Queue',
  description: 'Tasks requiring manual intervention or approval',
  actions: [
    { text: 'Auto-assign Tasks', path: auto_assign_manual_tasks_path, method: :post, 
      class: 'btn-secondary', data: { turbo_confirm: 'Auto-assign unassigned tasks to available users?' } },
    current_user.admin? ? { text: 'Clear Stale', path: clear_stale_manual_tasks_path, method: :post, 
      class: 'btn-ghost', data: { turbo_confirm: 'Clear stale task assignments?' } } : nil
  ].compact
%>

<div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
  <!-- Sidebar with statistics and filters -->
  <div class="lg:col-span-1 space-y-6">
    <!-- Queue Statistics -->
    <div class="bg-white rounded-lg shadow p-6">
      <h3 class="text-lg font-semibold mb-4">Queue Statistics</h3>
      <dl class="space-y-3">
        <div>
          <dt class="text-sm text-gray-500">Total Pending</dt>
          <dd class="text-2xl font-bold text-gray-900"><%= @statistics[:statistics][:total_pending] %></dd>
        </div>
        <div>
          <dt class="text-sm text-gray-500">Unassigned</dt>
          <dd class="text-xl font-semibold text-amber-600"><%= @statistics[:statistics][:unassigned] %></dd>
        </div>
        <div>
          <dt class="text-sm text-gray-500">Average Wait Time</dt>
          <dd class="text-lg text-gray-700"><%= distance_of_time_in_words(@statistics[:statistics][:average_wait_time]) %></dd>
        </div>
      </dl>
    </div>

    <!-- Priority Breakdown -->
    <div class="bg-white rounded-lg shadow p-6">
      <h3 class="text-lg font-semibold mb-4">By Priority</h3>
      <div class="space-y-2">
        <div class="flex items-center justify-between">
          <span class="text-sm font-medium text-red-600">High Priority</span>
          <span class="text-sm font-bold"><%= @statistics[:statistics][:by_priority][:high] %></span>
        </div>
        <div class="flex items-center justify-between">
          <span class="text-sm font-medium text-yellow-600">Medium Priority</span>
          <span class="text-sm font-bold"><%= @statistics[:statistics][:by_priority][:medium] %></span>
        </div>
        <div class="flex items-center justify-between">
          <span class="text-sm font-medium text-green-600">Low Priority</span>
          <span class="text-sm font-bold"><%= @statistics[:statistics][:by_priority][:low] %></span>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow p-6">
      <h3 class="text-lg font-semibold mb-4">Filters</h3>
      <%= form_with url: manual_tasks_path, method: :get, data: { turbo_frame: "manual_tasks" } do |f| %>
        <div class="space-y-4">
          <div>
            <%= f.check_box :assigned_to_me, class: "mr-2", onchange: "this.form.requestSubmit()" %>
            <%= f.label :assigned_to_me, "My Tasks Only" %>
          </div>
          <div>
            <%= f.label :pipeline_name, "Pipeline", class: "block text-sm font-medium text-gray-700" %>
            <%= f.select :pipeline_name, 
              options_for_select(
                @statistics[:statistics][:by_pipeline].map { |name, count| ["#{name} (#{count})", name] },
                params[:pipeline_name]
              ),
              { include_blank: "All Pipelines" },
              class: "mt-1 block w-full rounded-md border-gray-300",
              onchange: "this.form.requestSubmit()"
            %>
          </div>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Main task list -->
  <div class="lg:col-span-3">
    <%= turbo_frame_tag "manual_tasks" do %>
      <% if @tasks.any? %>
        <div class="space-y-4">
          <% @tasks.each do |task| %>
            <%= turbo_frame_tag task do %>
              <div class="bg-white rounded-lg shadow hover:shadow-md transition-shadow">
                <div class="p-6">
                  <div class="flex items-start justify-between">
                    <div class="flex-1">
                      <div class="flex items-center gap-3 mb-2">
                        <h3 class="text-lg font-semibold">
                          <%= link_to task.name, manual_task_path(task), 
                            class: "text-gray-900 hover:text-blue-600",
                            data: { turbo_frame: "_top" } %>
                        </h3>
                        <span class="<%= task.execution_mode_badge_class %> px-2 py-1 text-xs rounded-full">
                          <%= task.execution_mode.humanize %>
                        </span>
                        <span class="<%= task.status_badge_class %> px-2 py-1 text-xs rounded-full">
                          <%= task.status.humanize %>
                        </span>
                      </div>
                      
                      <p class="text-gray-600 mb-3"><%= truncate(task.description, length: 150) %></p>
                      
                      <div class="flex items-center gap-4 text-sm text-gray-500">
                        <span class="flex items-center gap-1">
                          <%= render 'shared/icons/clock' %>
                          <%= time_ago_in_words(task.created_at) %> ago
                        </span>
                        <span>Pipeline: <%= task.pipeline_execution.pipeline_name %></span>
                        <% if task.assignee %>
                          <span class="flex items-center gap-1">
                            Assigned to: <strong><%= task.assignee.name %></strong>
                          </span>
                        <% else %>
                          <span class="text-amber-600 font-medium">Unassigned</span>
                        <% end %>
                      </div>
                    </div>
                    
                    <div class="flex items-center gap-2 ml-4">
                      <span class="text-2xl font-bold text-gray-400">
                        <%= task.priority %>
                      </span>
                    </div>
                  </div>
                  
                  <div class="mt-4 flex items-center gap-2">
                    <% if task.can_execute? && (task.assignee == current_user || current_user.admin?) %>
                      <% if task.status == 'waiting_approval' %>
                        <%= link_to "Approve", approve_manual_task_path(task), 
                          class: "btn btn-primary btn-sm",
                          data: { turbo_frame: "_top" } %>
                        <%= link_to "Reject", reject_manual_task_path(task), 
                          class: "btn btn-danger btn-sm",
                          data: { turbo_frame: "_top" } %>
                      <% else %>
                        <%= link_to "Execute", execute_manual_task_path(task), 
                          class: "btn btn-primary btn-sm",
                          data: { turbo_frame: "_top" } %>
                      <% end %>
                    <% elsif task.assignee.nil? && task.execution_mode == 'manual' %>
                      <%= button_to "Assign to Me", 
                        assign_manual_task_path(task), 
                        method: :post,
                        class: "btn btn-secondary btn-sm",
                        data: { turbo_method: :post } %>
                    <% end %>
                    
                    <%= link_to "View Details", manual_task_path(task), 
                      class: "btn btn-ghost btn-sm",
                      data: { turbo_frame: "_top" } %>
                  </div>
                </div>
              </div>
            <% end %>
          <% end %>
        </div>
        
        <div class="mt-6">
          <%= paginate @tasks %>
        </div>
      <% else %>
        <div class="bg-white rounded-lg shadow p-12 text-center">
          <%= render 'shared/icons/check-circle', class: "w-16 h-16 text-green-500 mx-auto mb-4" %>
          <h3 class="text-xl font-semibold text-gray-900 mb-2">No Manual Tasks</h3>
          <p class="text-gray-600">All tasks are either completed or running automatically.</p>
        </div>
      <% end %>
    <% end %>
  </div>
</div>

<!-- Real-time updates channel -->
<%= turbo_stream_from "manual_task_queue" %>