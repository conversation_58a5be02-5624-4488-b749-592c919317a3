<!-- Premium Footer -->
<footer class="relative overflow-hidden" style="background: linear-gradient(135deg, #1f2937 0%, #374151 100%);">
  <!-- Premium Background Elements -->
  <div class="absolute inset-0">
    <div class="absolute top-10 left-10 w-64 h-64 bg-white/5 rounded-full mix-blend-overlay filter blur-3xl"></div>
    <div class="absolute bottom-10 right-10 w-80 h-80 bg-white/3 rounded-full mix-blend-overlay filter blur-3xl"></div>
  </div>

  <div class="premium-container relative z-10 py-16">
    <!-- Premium Integrations Showcase -->
    <div class="premium-mb-16">
      <div class="premium-text-center premium-mb-12">
        <h2 class="premium-heading-2 text-white premium-mb-4"
            role="heading"
            aria-level="2"
            aria-label="Trusted Integrations">
          Trusted Integrations
        </h2>
        <p class="premium-body-large text-white/80 max-w-2xl mx-auto">
          Connect with your favorite tools and unlock AI-powered insights across your entire business ecosystem
        </p>
      </div>

      <!-- Dynamic Integrations Grid -->
      <div class="premium-grid premium-grid-sm-2 premium-grid-md-4 premium-grid-lg-5 gap-6"
           role="list"
           aria-label="Available integrations">
        <% if defined?(@integrations) && @integrations.present? %>
          <% @integrations.first(10).each do |integration| %>
            <div class="footer-integration-card group premium-card premium-card-glass relative overflow-hidden text-center p-4"
                 style="background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(15px); border: 1px solid rgba(255, 255, 255, 0.2);"
                 role="listitem"
                 aria-label="<%= integration[:name] %> integration with <%= integration[:ai_feature] %>">

              <!-- Integration Icon -->
              <div class="integration-icon w-10 h-10 bg-gradient-to-r from-white/20 to-white/10 rounded-xl flex items-center justify-center mx-auto premium-mb-3 shadow-lg">
                <div class="w-5 h-5 bg-white rounded-lg flex items-center justify-center">
                  <div class="w-2.5 h-2.5 bg-gradient-to-r from-purple-500 to-blue-500 rounded-sm"></div>
                </div>
              </div>

              <!-- Integration Name -->
              <div class="premium-body-small font-semibold text-white premium-mb-2 truncate">
                <%= integration[:name] %>
              </div>

              <!-- AI Feature Badge -->
              <div class="premium-body-tiny text-white/70 bg-white/10 px-2 py-1 rounded-full text-xs truncate">
                <%= integration[:ai_feature] %>
              </div>

              <!-- Premium Hover Effect -->
              <div class="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-blue-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl"></div>
            </div>
          <% end %>
        <% else %>
          <!-- Fallback integrations if data not available -->
          <% %w[Shopify QuickBooks Stripe Mailchimp Analytics HubSpot Zendesk Slack Salesforce Airtable].each do |integration| %>
            <div class="footer-integration-card group premium-card premium-card-glass relative overflow-hidden text-center p-4"
                 style="background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(15px); border: 1px solid rgba(255, 255, 255, 0.2);"
                 role="listitem"
                 aria-label="<%= integration %> integration with AI enhancement">

              <div class="integration-icon w-10 h-10 bg-gradient-to-r from-white/20 to-white/10 rounded-xl flex items-center justify-center mx-auto premium-mb-3 shadow-lg">
                <div class="w-5 h-5 bg-white rounded-lg flex items-center justify-center">
                  <div class="w-2.5 h-2.5 bg-gradient-to-r from-purple-500 to-blue-500 rounded-sm"></div>
                </div>
              </div>

              <div class="premium-body-small font-semibold text-white premium-mb-2 truncate">
                <%= integration %>
              </div>

              <div class="premium-body-tiny text-white/70 bg-white/10 px-2 py-1 rounded-full text-xs">
                AI Enhanced
              </div>

              <div class="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-blue-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl"></div>
            </div>
          <% end %>
        <% end %>
      </div>
    </div>

    <!-- Footer Links Grid -->
    <div class="premium-grid premium-grid-md-2 premium-grid-lg-3 gap-12">

      <!-- Premium Company Info -->
      <div class="lg:col-span-1">
        <div class="flex items-center space-x-4 premium-mb-8">
          <div class="flex h-12 w-12 items-center justify-center rounded-2xl bg-gradient-to-r from-purple-500 to-blue-500 shadow-xl">
            <svg class="h-7 w-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
          </div>
          <span class="premium-heading-2 text-white">DataReflow</span>
        </div>

        <p class="premium-body-large text-white/80 premium-mb-8 max-w-lg leading-relaxed">
          Transforming business data into actionable insights with autonomous AI for growing companies worldwide.
        </p>

        <div class="flex flex-col space-y-4">
          <a href="/status" class="inline-flex items-center premium-body text-white/70 hover:text-white transition-colors duration-300 group">
            <div class="w-3 h-3 bg-gradient-to-r from-green-400 to-emerald-400 rounded-full mr-3 group-hover:scale-110 transition-transform duration-300"></div>
            All systems operational
          </a>

          <!-- Premium Social Links -->
          <div class="flex space-x-4 mt-6">
            <a href="#" class="w-10 h-10 bg-white/10 backdrop-blur-sm rounded-xl flex items-center justify-center text-white/70 hover:text-white hover:bg-white/20 transition-all duration-300">
              <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
              </svg>
            </a>
            <a href="#" class="w-10 h-10 bg-white/10 backdrop-blur-sm rounded-xl flex items-center justify-center text-white/70 hover:text-white hover:bg-white/20 transition-all duration-300">
              <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
              </svg>
            </a>
            <a href="#" class="w-10 h-10 bg-white/10 backdrop-blur-sm rounded-xl flex items-center justify-center text-white/70 hover:text-white hover:bg-white/20 transition-all duration-300">
              <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24c6.624 0 11.99-5.367 11.99-11.987C24.007 5.367 18.641.001 12.017.001z"/>
              </svg>
            </a>
          </div>
        </div>
      </div>

      <!-- Premium Quick Links -->
      <div>
        <h3 class="premium-heading-3 text-white premium-mb-8">Platform</h3>
        <ul class="space-y-4">
          <li><%= link_to dashboard_path, class: "premium-body text-white/70 hover:text-white transition-colors duration-300 flex items-center group" do %>
            <svg class="h-4 w-4 mr-3 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
            Dashboard
          <% end %></li>
          <li><%= link_to '#', class: "premium-body text-white/70 hover:text-white transition-colors duration-300 flex items-center group" do %>
            <svg class="h-4 w-4 mr-3 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path>
            </svg>
            Data Sources
          <% end %></li>
          <li><%= link_to  '#', class: "premium-body text-white/70 hover:text-white transition-colors duration-300 flex items-center group" do %>
            <svg class="h-4 w-4 mr-3 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
            Analytics
          <% end %></li>
          <li><%= link_to '#', class: "premium-body text-white/70 hover:text-white transition-colors duration-300 flex items-center group" do %>
            <svg class="h-4 w-4 mr-3 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a1 1 0 01-1-1V9a1 1 0 011-1h1a2 2 0 100-4H4a1 1 0 01-1-1V4a1 1 0 011-1h3a1 1 0 001-1v-1a2 2 0 114 0z"></path>
            </svg>
            Integrations
          <% end %></li>
        </ul>
      </div>

      <!-- Premium Support -->
      <div>
        <h3 class="premium-heading-3 text-white premium-mb-8">Support</h3>
        <ul class="space-y-4">
          <li><a href="/help" class="premium-body text-white/70 hover:text-white transition-colors duration-300 flex items-center group">
            <svg class="h-4 w-4 mr-3 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            Help Center
          </a></li>
          <li><a href="/docs" class="premium-body text-white/70 hover:text-white transition-colors duration-300 flex items-center group">
            <svg class="h-4 w-4 mr-3 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
            </svg>
            Documentation
          </a></li>
          <li><a href="/api/docs" class="premium-body text-white/70 hover:text-white transition-colors duration-300 flex items-center group">
            <svg class="h-4 w-4 mr-3 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
            </svg>
            API Reference
          </a></li>
          <li><a href="/contact" class="premium-body text-white/70 hover:text-white transition-colors duration-300 flex items-center group">
            <svg class="h-4 w-4 mr-3 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
            </svg>
            Contact Support
          </a></li>
        </ul>
      </div>
    </div>

    <!-- Premium Bottom Section -->
    <div class="mt-16 pt-8 border-t border-white/20">
      <div class="flex flex-col md:flex-row md:items-center md:justify-between">
        <div class="flex flex-wrap gap-8 md:order-2 mb-6 md:mb-0">
          <a href="/privacy" class="premium-body text-white/70 hover:text-white transition-colors duration-300 flex items-center group">
            <svg class="h-4 w-4 mr-2 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
            </svg>
            Privacy Policy
          </a>
          <a href="/terms" class="premium-body text-white/70 hover:text-white transition-colors duration-300 flex items-center group">
            <svg class="h-4 w-4 mr-2 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            Terms of Service
          </a>
          <a href="/security" class="premium-body text-white/70 hover:text-white transition-colors duration-300 flex items-center group">
            <svg class="h-4 w-4 mr-2 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
            </svg>
            Security
          </a>
        </div>
        <div class="md:order-1 flex flex-col sm:flex-row sm:items-center gap-4">
          <p class="premium-body text-white/60">
            &copy; <%= Date.current.year %> DataReflow. All rights reserved.
          </p>
          <div class="flex items-center gap-2">
            <span class="premium-body-small text-white/50">Built with</span>
            <svg class="h-4 w-4 text-red-400" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
            </svg>
            <span class="premium-body-small text-white/50">for growing businesses</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</footer>