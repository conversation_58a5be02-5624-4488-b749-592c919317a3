<!-- Enhanced Upcube-inspired Desktop Sidebar -->
<div class="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col" id="desktop-sidebar" data-controller="sidebar">
  <div class="flex grow flex-col gap-y-4 overflow-y-auto bg-white border-r border-gray-200 px-6 pb-4">

    <!-- Logo Section -->
    <div class="flex h-16 shrink-0 items-center">
      <%= link_to root_path, class: "flex items-center space-x-3 group" do %>
        <div class="flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-indigo-600 to-indigo-700 shadow-lg group-hover:shadow-xl transition-shadow">
          <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
          </svg>
        </div>
        <div>
          <span class="text-xl font-bold text-gray-900">DataReflow</span>
          <div class="text-xs text-gray-500 font-medium">Data Refinery Platform</div>
        </div>
      <% end %>
    </div>

    <!-- Enhanced System Overview with Real-time Metrics -->
    <div class="bg-white rounded-xl p-4 border border-gray-200 shadow-sm">
      <div class="flex items-center justify-between mb-3">
        <h4 class="text-sm font-semibold text-gray-900">System Overview</h4>
        <div class="flex items-center gap-2">
          <div class="h-2 w-2 bg-green-500 rounded-full animate-pulse" title="All systems operational"></div>
          <button class="text-xs text-gray-500 hover:text-gray-700 font-medium transition-colors"
                  data-action="click->sidebar#refreshStats"
                  data-sidebar-target="refreshButton">
            <svg class="h-3 w-3 inline transition-transform" data-sidebar-target="refreshIcon" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
              <path stroke-linecap="round" stroke-linejoin="round" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </button>
        </div>
      </div>
      <div class="grid grid-cols-2 gap-3 mb-3">
        <div class="text-center p-3 bg-blue-50 rounded-lg border border-blue-100">
          <div class="text-lg font-bold text-blue-600" data-sidebar-target="sourceCount">
            <%= @data_sources&.count || 0 %>
          </div>
          <div class="text-xs text-gray-600 font-medium">Active Sources</div>
        </div>
        <div class="text-center p-3 bg-green-50 rounded-lg border border-green-100">
          <div class="text-lg font-bold text-green-600" data-sidebar-target="qualityScore">98%</div>
          <div class="text-xs text-gray-600 font-medium">Data Quality</div>
        </div>
      </div>
      <div class="grid grid-cols-2 gap-3">
        <div class="text-center p-2 bg-amber-50 rounded-lg border border-amber-100">
          <div class="text-sm font-bold text-amber-600" data-sidebar-target="processingJobs">12</div>
          <div class="text-xs text-gray-600 font-medium">Processing</div>
        </div>
        <div class="text-center p-2 bg-purple-50 rounded-lg border border-purple-100">
          <div class="text-sm font-bold text-purple-600" data-sidebar-target="alertCount">3</div>
          <div class="text-xs text-gray-600 font-medium">Alerts</div>
        </div>
      </div>
    </div>

    <!-- Enhanced Quick Actions with Categories -->
    <div class="bg-white rounded-xl p-4 border border-gray-200 shadow-sm">
      <div class="flex items-center justify-between mb-3">
        <h4 class="text-sm font-semibold text-gray-900">Quick Actions</h4>
        <button class="text-xs text-gray-500 hover:text-gray-700 transition-colors"
                data-action="click->sidebar#expandQuickActions"
                data-sidebar-target="quickActionsToggle">
          <svg class="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
        </button>
      </div>

      <div class="space-y-2" data-sidebar-target="quickActionsList">
        <!-- Data Management Actions -->
        <div class="border-b border-gray-100 pb-2 mb-2">
          <div class="text-xs font-medium text-gray-500 uppercase tracking-wide mb-2">Data Management</div>
          <%= link_to new_data_source_path,
              class: "flex items-center gap-3 px-3 py-2 text-sm text-gray-700 rounded-lg hover:bg-indigo-50 hover:text-indigo-700 transition-all group" do %>
            <div class="flex h-7 w-7 items-center justify-center rounded-lg bg-indigo-100 group-hover:bg-indigo-200 transition-colors">
              <svg class="h-4 w-4 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M12 4v16m8-8H4" />
              </svg>
            </div>
            <span class="font-medium">Add Data Source</span>
          <% end %>

          <%= link_to analytics_path,
              class: "flex items-center gap-3 px-3 py-2 text-sm text-gray-700 rounded-lg hover:bg-purple-50 hover:text-purple-700 transition-all group" do %>
            <div class="flex h-7 w-7 items-center justify-center rounded-lg bg-purple-100 group-hover:bg-purple-200 transition-colors">
              <svg class="h-4 w-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 013 19.875v-6.75zM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V8.625zM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V4.125z" />
              </svg>
            </div>
            <span class="font-medium">Run Analysis</span>
          <% end %>
        </div>

        <!-- Team Management Actions -->
        <div class="border-b border-gray-100 pb-2 mb-2">
          <div class="text-xs font-medium text-gray-500 uppercase tracking-wide mb-2">Team Management</div>
          <%= link_to new_user_path,
              class: "flex items-center gap-3 px-3 py-2 text-sm text-gray-700 rounded-lg hover:bg-green-50 hover:text-green-700 transition-all group" do %>
            <div class="flex h-7 w-7 items-center justify-center rounded-lg bg-green-100 group-hover:bg-green-200 transition-colors">
              <svg class="h-4 w-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M18 7.5v3m0 0v3m0-3h3m-3 0h-3m-1.5-6.75H12v2.25m0 0h3.75m-3.75 0h-.375a1.125 1.125 0 00-1.125 1.125v9.75c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V10.125a1.125 1.125 0 00-1.125-1.125H18.75" />
              </svg>
            </div>
            <span class="font-medium">Invite Team Member</span>
          <% end %>

          <button class="flex items-center gap-3 px-3 py-2 text-sm text-gray-700 rounded-lg hover:bg-blue-50 hover:text-blue-700 transition-all group w-full"
                  data-action="click->sidebar#createReport">
            <div class="flex h-7 w-7 items-center justify-center rounded-lg bg-blue-100 group-hover:bg-blue-200 transition-colors">
              <svg class="h-4 w-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <span class="font-medium">Generate Report</span>
          </button>
        </div>

        <!-- Quick Settings -->
        <div class="space-y-1">
          <div class="text-xs font-medium text-gray-500 uppercase tracking-wide mb-2">Quick Settings</div>
          <button class="flex items-center gap-3 px-3 py-2 text-sm text-gray-700 rounded-lg hover:bg-gray-100 transition-all group w-full"
                  data-action="click->sidebar#toggleNotifications">
            <div class="flex h-7 w-7 items-center justify-center rounded-lg bg-gray-100 group-hover:bg-gray-200 transition-colors">
              <svg class="h-4 w-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
              </svg>
            </div>
            <span class="font-medium">Notifications</span>
            <div class="ml-auto">
              <div class="h-2 w-2 bg-green-500 rounded-full" data-sidebar-target="notificationStatus"></div>
            </div>
          </button>
        </div>
      </div>
    </div>

    <!-- Navigation -->
    <nav class="flex flex-1 flex-col">
      <ul role="list" class="flex flex-1 flex-col gap-y-6">

        <!-- Main Navigation -->
        <li>
          <div class="text-xs font-semibold leading-6 text-gray-400 uppercase tracking-wider mb-2">Main</div>
          <ul role="list" class="-mx-2 space-y-1">

            <!-- Dashboard -->
            <li>
              <%= link_to dashboard_path, class: nav_link_classes('dashboard') do %>
                <svg class="h-5 w-5 shrink-0" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25" />
                </svg>
                <span>Dashboard</span>
                <span class="ml-auto flex h-5 w-5 items-center justify-center rounded-full bg-blue-100 text-xs font-medium text-blue-600">
                  <%= @new_alerts_count || 0 %>
                </span>
              <% end %>
            </li>

            <!-- Analytics -->
            <li>
              <%= link_to analytics_path, class: nav_link_classes('analytics') do %>
                <svg class="h-5 w-5 shrink-0" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 013 19.875v-6.75zM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V8.625zM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V4.125z" />
                </svg>
                <span>Analytics</span>
              <% end %>
            </li>

            <!-- Data Sources with submenu -->
            <li>
              <div data-controller="submenu">
                <button class="flex w-full items-center gap-x-3 rounded-lg px-3 py-2 text-left text-sm leading-6 font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50 transition-colors" 
                        data-action="click->submenu#toggle"
                        data-submenu-target="button">
                  <svg class="h-5 w-5 shrink-0" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M20.25 6.375c0 2.278-3.694 4.125-8.25 4.125S3.75 8.653 3.75 6.375m16.5 0c0-2.278-3.694-4.125-8.25-4.125S3.75 4.097 3.75 6.375m16.5 0v11.25c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125V6.375m16.5 0v3.75m-16.5-3.75v3.75m16.5 0v3.75C20.25 18.653 16.556 20.5 12 20.5s-8.25-1.847-8.25-4.125v-3.75m16.5 0c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125" />
                  </svg>
                  <span class="flex-1 text-left">Data Sources</span>
                  <svg class="h-4 w-4 shrink-0 transition-transform" data-submenu-target="chevron" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
                  </svg>
                </button>
                
                <ul class="hidden ml-6 mt-1 space-y-1" data-submenu-target="menu">
                  <li>
                    <%= link_to data_sources_path, class: "flex items-center gap-3 rounded-lg px-3 py-2 text-sm text-gray-600 hover:bg-gray-100 hover:text-gray-900 transition-colors" do %>
                      <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      All Sources
                    <% end %>
                  </li>
                  <li>
                    <%= link_to quality_data_sources_path, class: "flex items-center gap-3 rounded-lg px-3 py-2 text-sm text-gray-600 hover:bg-gray-100 hover:text-gray-900 transition-colors" do %>
                      <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 01-1.043 3.296 3.745 3.745 0 01-3.296 1.043A3.745 3.745 0 0112 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 01-3.296-1.043 3.745 3.745 0 01-1.043-3.296A3.745 3.745 0 013 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 011.043-3.296 3.746 3.746 0 013.296-1.043A3.746 3.746 0 0112 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 013.296 1.043 3.746 3.746 0 011.043 3.296A3.745 3.745 0 0121 12z" />
                      </svg>
                      Data Quality
                    <% end %>
                  </li>
                  <li>
                    <%= link_to new_data_source_path, class: "flex items-center gap-3 rounded-lg px-3 py-2 text-sm text-gray-600 hover:bg-gray-100 hover:text-gray-900 transition-colors" do %>
                      <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 4v16m8-8H4" />
                      </svg>
                      Add New Source
                    <% end %>
                  </li>
                </ul>
              </div>
            </li>

            <!-- Pipeline Dashboard -->
            <li>
              <%= link_to pipeline_dashboard_index_path, class: nav_link_classes('pipeline_dashboard') do %>
                <svg class="h-5 w-5 shrink-0" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 3v11.25A2.25 2.25 0 006 16.5h2.25M3.75 3h-1.5m1.5 0h16.5m0 0h1.5m-1.5 0v11.25A2.25 2.25 0 0118 16.5h-2.25m-7.5 0h7.5m-7.5 0l-1 3m8.5-3l1 3m0 0l.5 1.5m-.5-1.5h-9.5m0 0l-.5 1.5M9 11.25v1.5M12 9v3.75m3-6v6" />
                </svg>
                <span>Pipeline Dashboard</span>
                <% if @running_pipelines_count && @running_pipelines_count > 0 %>
                  <span class="ml-auto flex h-5 w-5 items-center justify-center rounded-full bg-blue-100 text-xs font-medium text-blue-600">
                    <%= @running_pipelines_count %>
                  </span>
                <% end %>
              <% end %>
            </li>

            <!-- Manual Tasks -->
            <li>
              <%= link_to manual_tasks_path, class: nav_link_classes('manual_tasks') do %>
                <svg class="h-5 w-5 shrink-0" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75m-3-7.036A11.959 11.959 0 013.598 6 11.99 11.99 0 003 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285z" />
                </svg>
                <span>Manual Tasks</span>
                <% if @manual_tasks_count && @manual_tasks_count > 0 %>
                  <span class="ml-auto flex h-5 w-5 items-center justify-center rounded-full bg-yellow-100 text-xs font-medium text-yellow-600">
                    <%= @manual_tasks_count %>
                  </span>
                <% end %>
              <% end %>
            </li>
          </ul>
        </li>

        <!-- Applications Section -->
        <li>
          <div class="text-xs font-semibold leading-6 text-gray-400 uppercase tracking-wider mb-2">Applications</div>
          <ul role="list" class="-mx-2 space-y-1">
            
            <!-- E-commerce -->
            <li>
              <%= link_to '#ecommerce', class: nav_link_classes('ecommerce') do %>
                <svg class="h-5 w-5 shrink-0" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                </svg>
                <span>E-commerce</span>
                <span class="ml-auto text-xs bg-green-100 text-green-600 px-2 py-0.5 rounded-full font-medium">
                  Active
                </span>
              <% end %>
            </li>

            <!-- Marketing -->
            <li>
              <%= link_to '#marketing', class: nav_link_classes('marketing') do %>
                <svg class="h-5 w-5 shrink-0" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M10.34 15.84c-.688-.06-1.386-.09-2.09-.09H7.5a4.5 4.5 0 110-9h.75c.704 0 1.402-.03 2.09-.09m0 9.18c.253.962.584 1.892.985 2.783.247.55.06 1.21-.463 1.511l-.657.38c-.551.318-1.26.117-1.527-.461a20.845 20.845 0 01-1.44-4.282m3.102.069a18.03 18.03 0 01-.59-4.59c0-1.586.205-3.124.59-4.59m0 9.18a23.848 23.848 0 018.835 2.535M10.34 6.66a23.847 23.847 0 008.835-2.535m0 0A23.74 23.74 0 0018.795 3m.38 1.125l1.25 1.25M1.5 4.875l1.25 1.25m0 0l-1.262 12.621a23.906 23.906 0 0015.777 0L18.75 6.125m0 0l1.25-1.25" />
                </svg>
                <span>Marketing</span>
                <span class="ml-auto text-xs bg-yellow-100 text-yellow-600 px-2 py-0.5 rounded-full font-medium">
                  Beta
                </span>
              <% end %>
            </li>

            <!-- Finance -->
            <li>
              <%= link_to '#finance', class: nav_link_classes('finance') do %>
                <svg class="h-5 w-5 shrink-0" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v12m-3-2.818l.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.375-.203-1.769-.55-.879-.78-.879-2.426 0-3.205.879-.78 2.074-.78 2.953 0l.15.15M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5" />
                </svg>
                <span>Finance</span>
              <% end %>
            </li>
          </ul>
        </li>

        <!-- AI-Powered Features Section -->
        <li>
          <div class="text-xs font-semibold leading-6 text-gray-400 uppercase tracking-wider mb-2">AI Features</div>
          <ul role="list" class="-mx-2 space-y-1">
            
            <!-- Interactive Presentations -->
            <li>
              <%= link_to dashboard_ai_interactive_presentations_path, class: nav_link_classes('ai_presentations') do %>
                <svg class="h-5 w-5 shrink-0" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 3v11.25A2.25 2.25 0 006 16.5h2.25M3.75 3h-1.5m1.5 0h16.5m0 0h1.5m-1.5 0v11.25A2.25 2.25 0 0118 16.5h-2.25m-7.5 0h7.5m-7.5 0l-1 3m8.5-3l1 3m0 0l-1-3m1 3l-1-3m-16.5-3h9v-9h-9v9z" />
                </svg>
                <span>Presentations</span>
                <span class="ml-auto text-xs bg-purple-100 text-purple-600 px-2 py-0.5 rounded-full font-medium">
                  AI
                </span>
              <% end %>
            </li>

            <!-- AI Queries -->
            <li>
              <%= link_to ai_queries_path, class: nav_link_classes('ai_queries') do %>
                <svg class="h-5 w-5 shrink-0" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 001.423 1.423l1.183.394-1.183.394a2.25 2.25 0 00-1.423 1.423z" />
                </svg>
                <span>AI Queries</span>
                <span class="ml-auto text-xs bg-blue-100 text-blue-600 px-2 py-0.5 rounded-full font-medium">
                  Smart
                </span>
              <% end %>
            </li>

            <!-- Real-time Analytics -->
            <li>
              <%= link_to dashboard_ai_real_time_analytics_path, class: nav_link_classes('ai_analytics') do %>
                <svg class="h-5 w-5 shrink-0" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z" />
                </svg>
                <span>Real-time Analytics</span>
                <span class="ml-auto text-xs bg-green-100 text-green-600 px-2 py-0.5 rounded-full font-medium">
                  Live
                </span>
              <% end %>
            </li>

            <!-- BI Agent -->
            <li>
              <%= link_to dashboard_ai_bi_agent_index_path, class: nav_link_classes('bi_agent') do %>
                <svg class="h-5 w-5 shrink-0" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z" />
                </svg>
                <span>BI Agent</span>
                <span class="ml-auto text-xs bg-indigo-100 text-indigo-600 px-2 py-0.5 rounded-full font-medium">
                  Agent
                </span>
              <% end %>
            </li>

            <!-- Data Integration -->
            <li>
              <%= link_to dashboard_ai_data_integration_index_path, class: nav_link_classes('ai_integration') do %>
                <svg class="h-5 w-5 shrink-0" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M13.19 8.688a4.5 4.5 0 011.242 7.244l-4.5 4.5a4.5 4.5 0 01-6.364-6.364l1.757-1.757m13.35-.622l1.757-1.757a4.5 4.5 0 00-6.364-6.364l-4.5 4.5a4.5 4.5 0 001.242 7.244" />
                </svg>
                <span>Data Integration</span>
                <span class="ml-auto text-xs bg-orange-100 text-orange-600 px-2 py-0.5 rounded-full font-medium">
                  Auto
                </span>
              <% end %>
            </li>
          </ul>
        </li>

        <!-- Management Section -->
        <li>
          <div class="text-xs font-semibold leading-6 text-gray-400 uppercase tracking-wider mb-2">Management</div>
          <ul role="list" class="-mx-2 space-y-1">
            
            <!-- Team -->
            <li>
              <%= link_to users_path, class: nav_link_classes('team') do %>
                <svg class="h-5 w-5 shrink-0" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M18 18.72a9.094 9.094 0 003.741-.479 3 3 0 00-4.682-2.72m.94 3.198l.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0112 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 016 18.719m12 0a5.971 5.971 0 00-.941-3.197m0 0A5.995 5.995 0 0012 12.75a5.995 5.995 0 00-5.058 2.772m0 0a3 3 0 00-4.681 2.72 8.986 8.986 0 003.74.477m.94-3.197a5.971 5.971 0 00-.94 3.197M15 6.75a3 3 0 11-6 0 3 3 0 016 0zm6 3a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0zm-13.5 0a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z" />
                </svg>
                <span>Team</span>
              <% end %>
            </li>

            <!-- Settings -->
            <li>
              <%= link_to organization_path, class: nav_link_classes('settings') do %>
                <svg class="h-5 w-5 shrink-0" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z" />
                  <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                <span>Settings</span>
              <% end %>
            </li>
          </ul>
        </li>
      </ul>
    </nav>

    <!-- Enhanced Bottom Section -->
    <div class="mt-auto space-y-3">
      <!-- Recent Activity Feed -->
      <div class="bg-white rounded-xl p-3 border border-gray-200 shadow-sm">
        <div class="flex items-center justify-between mb-2">
          <h4 class="text-xs font-semibold text-gray-900 uppercase tracking-wide">Recent Activity</h4>
          <button class="text-xs text-gray-500 hover:text-gray-700 transition-colors"
                  data-action="click->sidebar#refreshActivity"
                  data-sidebar-target="activityRefresh">
            <svg class="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
              <path stroke-linecap="round" stroke-linejoin="round" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </button>
        </div>
        <div class="space-y-2" data-sidebar-target="activityFeed">
          <div class="flex items-center gap-2 text-xs">
            <div class="h-1.5 w-1.5 bg-blue-500 rounded-full"></div>
            <span class="text-gray-600 truncate">Shopify sync completed</span>
            <span class="text-gray-400 ml-auto">2m</span>
          </div>
          <div class="flex items-center gap-2 text-xs">
            <div class="h-1.5 w-1.5 bg-green-500 rounded-full"></div>
            <span class="text-gray-600 truncate">User invited successfully</span>
            <span class="text-gray-400 ml-auto">5m</span>
          </div>
          <div class="flex items-center gap-2 text-xs">
            <div class="h-1.5 w-1.5 bg-purple-500 rounded-full"></div>
            <span class="text-gray-600 truncate">Report generated</span>
            <span class="text-gray-400 ml-auto">12m</span>
          </div>
        </div>
        <button class="text-xs text-indigo-600 hover:text-indigo-800 font-medium mt-2 w-full text-left"
                data-action="click->sidebar#viewAllActivity">
          View all activity →
        </button>
      </div>

      <!-- Enhanced System Status -->
      <div class="bg-white rounded-xl p-3 border border-gray-200 shadow-sm">
        <div class="flex items-center justify-between mb-2">
          <h4 class="text-xs font-semibold text-gray-900 uppercase tracking-wide">System Status</h4>
          <div class="flex items-center gap-1">
            <div class="h-2 w-2 bg-<%= @system_status[:health][:color] %>-500 rounded-full animate-pulse" data-realtime-dashboard-target="statusIndicator"></div>
            <span class="text-xs text-<%= @system_status[:health][:color] %>-600 font-medium" data-realtime-dashboard-target="statusText"><%= @system_status[:health][:text] %></span>
          </div>
        </div>
        <div class="space-y-2">
          <div class="flex items-center justify-between text-xs">
            <span class="text-gray-600">API Uptime</span>
            <span class="text-green-600 font-medium" data-realtime-dashboard-target="uptime"><%= @system_status[:uptime] %></span>
          </div>
          <div class="flex items-center justify-between text-xs">
            <span class="text-gray-600">Data Processing</span>
            <span class="text-blue-600 font-medium" data-realtime-dashboard-target="processingJobs"><%= pluralize(@system_status[:processing_jobs], 'job') %></span>
          </div>
          <div class="flex items-center justify-between text-xs">
            <span class="text-gray-600">Storage Used</span>
            <span class="text-purple-600 font-medium" data-realtime-dashboard-target="storageUsed"><%= @system_status[:storage_used] %></span>
          </div>
        </div>
        <button class="text-xs text-indigo-600 hover:text-indigo-800 font-medium mt-2 w-full text-left"
                data-action="click->sidebar#showSystemStatus">
          View detailed status →
        </button>
      </div>

      <!-- Enhanced Help & Support -->
      <div class="bg-white rounded-xl p-3 border border-gray-200 shadow-sm">
        <div class="space-y-2">
          <button class="flex items-center gap-2 px-2 py-1.5 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors w-full"
                  data-action="click->sidebar#showHelp">
            <div class="flex h-6 w-6 items-center justify-center rounded-lg bg-blue-100">
              <svg class="h-3 w-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <span class="font-medium">Help Center</span>
          </button>
          
          <button class="flex items-center gap-2 px-2 py-1.5 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors w-full"
                  data-action="click->sidebar#showKeyboardShortcuts">
            <div class="flex h-6 w-6 items-center justify-center rounded-lg bg-purple-100">
              <svg class="h-3 w-3 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
              </svg>
            </div>
            <span class="font-medium">Shortcuts</span>
            <span class="ml-auto text-xs bg-gray-100 text-gray-600 px-1.5 py-0.5 rounded font-mono">?</span>
          </button>
          
          <button class="flex items-center gap-2 px-2 py-1.5 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors w-full"
                  data-action="click->sidebar#showFeedback">
            <div class="flex h-6 w-6 items-center justify-center rounded-lg bg-green-100">
              <svg class="h-3 w-3 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M7 8h10m0 0V6a2 2 0 00-2-2H9a2 2 0 00-2 2v2m10 0v10a2 2 0 01-2 2H9a2 2 0 01-2-2V8m10 0L17 8" />
              </svg>
            </div>
            <span class="font-medium">Send Feedback</span>
          </button>
        </div>
      </div>

      <!-- Version Info -->
      <div class="text-center py-2">
        <div class="text-xs text-gray-500">
          DataReflow v2.1.0
        </div>
        <div class="text-xs text-gray-400">
          Rails 8.0.2 • Ruby 3.4.3
        </div>
      </div>
    </div>
  </div>
</div>
