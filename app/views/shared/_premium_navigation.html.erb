<!-- Premium Navigation with Glassmorphism Effects -->
<div class="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col" 
     id="desktop-sidebar" 
     data-controller="sidebar page-transition"
     data-page-transition-trigger-value="auto">
  
  <!-- Glassmorphic Sidebar Container -->
  <div class="flex grow flex-col gap-y-4 overflow-y-auto bg-white/80 backdrop-blur-xl border-r border-white/20 px-6 pb-4 shadow-2xl">
    
    <!-- Premium Logo Section -->
    <div class="flex h-20 shrink-0 items-center border-b border-white/10 pb-4">
      <%= link_to root_path, 
          class: "flex items-center space-x-3 group",
          data: {
            controller: "magnetic-button",
            action: "mousemove->magnetic-button#mouseMove mouseleave->magnetic-button#mouseLeave"
          } do %>
        <div class="relative">
          <div class="absolute inset-0 bg-gradient-to-br from-indigo-600 to-purple-600 rounded-2xl blur-lg opacity-50 group-hover:opacity-75 transition-opacity duration-500"></div>
          <div class="relative flex h-12 w-12 items-center justify-center rounded-2xl bg-gradient-to-br from-indigo-600 to-purple-700 shadow-xl group-hover:shadow-2xl transition-all duration-500 group-hover:scale-110">
            <svg class="h-7 w-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
              <path stroke-linecap="round" stroke-linejoin="round" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
          </div>
        </div>
        <div>
          <span class="text-2xl font-bold bg-gradient-to-r from-gray-900 via-indigo-900 to-purple-900 bg-clip-text text-transparent">
            DataReflow
          </span>
          <div class="text-xs text-slate-500 font-medium">Premium Data Platform</div>
        </div>
      <% end %>
    </div>

    <!-- Premium System Overview -->
    <div class="relative bg-white/50 backdrop-blur-sm rounded-2xl p-4 border border-white/20 shadow-xl hover:shadow-2xl transition-all duration-500">
      <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-purple-500/5 rounded-2xl"></div>
      <div class="relative">
        <div class="flex items-center justify-between mb-4">
          <h4 class="text-sm font-bold bg-gradient-to-r from-gray-900 to-indigo-900 bg-clip-text text-transparent">
            System Overview
          </h4>
          <div class="flex items-center gap-2">
            <div class="relative">
              <div class="absolute inset-0 bg-green-500 rounded-full blur-md opacity-50 animate-pulse"></div>
              <div class="relative h-2 w-2 bg-green-500 rounded-full animate-pulse"></div>
            </div>
            <button class="p-1 hover:bg-white/50 rounded-lg transition-all duration-300 group"
                    data-action="click->sidebar#refreshStats"
                    data-sidebar-target="refreshButton"
                    data-controller="ripple-effect"
                    data-action="click->ripple-effect#click">
              <svg class="h-4 w-4 text-slate-600 group-hover:text-indigo-600 transition-colors duration-300 group-hover:rotate-180" 
                   data-sidebar-target="refreshIcon" 
                   fill="none" 
                   stroke="currentColor" 
                   viewBox="0 0 24 24" 
                   stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </button>
          </div>
        </div>
        
        <!-- Animated Metrics Grid -->
        <div class="grid grid-cols-2 gap-3 mb-3">
          <div class="group relative overflow-hidden text-center p-3 bg-gradient-to-br from-blue-50/50 to-indigo-50/50 rounded-xl border border-blue-200/30 hover:border-blue-300/50 transition-all duration-500 hover:scale-105">
            <div class="absolute inset-0 bg-gradient-to-br from-blue-400/10 to-indigo-400/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            <div class="relative">
              <div class="text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent" 
                   data-sidebar-target="sourceCount">
                <%= @data_sources&.count || 0 %>
              </div>
              <div class="text-xs text-slate-600 font-semibold">Active Sources</div>
            </div>
          </div>
          
          <div class="group relative overflow-hidden text-center p-3 bg-gradient-to-br from-green-50/50 to-emerald-50/50 rounded-xl border border-green-200/30 hover:border-green-300/50 transition-all duration-500 hover:scale-105">
            <div class="absolute inset-0 bg-gradient-to-br from-green-400/10 to-emerald-400/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            <div class="relative">
              <div class="text-2xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent" 
                   data-sidebar-target="qualityScore">98%</div>
              <div class="text-xs text-slate-600 font-semibold">Data Quality</div>
            </div>
          </div>
        </div>
        
        <div class="grid grid-cols-2 gap-3">
          <div class="group relative overflow-hidden text-center p-2 bg-gradient-to-br from-amber-50/50 to-orange-50/50 rounded-xl border border-amber-200/30 hover:border-amber-300/50 transition-all duration-500 hover:scale-105">
            <div class="absolute inset-0 bg-gradient-to-br from-amber-400/10 to-orange-400/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            <div class="relative">
              <div class="text-lg font-bold bg-gradient-to-r from-amber-600 to-orange-600 bg-clip-text text-transparent" 
                   data-sidebar-target="processingJobs">12</div>
              <div class="text-xs text-slate-600 font-semibold">Processing</div>
            </div>
          </div>
          
          <div class="group relative overflow-hidden text-center p-2 bg-gradient-to-br from-purple-50/50 to-pink-50/50 rounded-xl border border-purple-200/30 hover:border-purple-300/50 transition-all duration-500 hover:scale-105">
            <div class="absolute inset-0 bg-gradient-to-br from-purple-400/10 to-pink-400/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            <div class="relative">
              <div class="text-lg font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent" 
                   data-sidebar-target="alertCount">3</div>
              <div class="text-xs text-slate-600 font-semibold">Alerts</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Premium Quick Actions -->
    <div class="relative bg-white/50 backdrop-blur-sm rounded-2xl p-4 border border-white/20 shadow-xl hover:shadow-2xl transition-all duration-500">
      <div class="absolute inset-0 bg-gradient-to-br from-indigo-500/5 to-purple-500/5 rounded-2xl"></div>
      <div class="relative">
        <div class="flex items-center justify-between mb-3">
          <h4 class="text-sm font-bold bg-gradient-to-r from-gray-900 to-indigo-900 bg-clip-text text-transparent">
            Quick Actions
          </h4>
          <button class="p-1 hover:bg-white/50 rounded-lg transition-all duration-300 group"
                  data-action="click->sidebar#expandQuickActions"
                  data-sidebar-target="quickActionsToggle"
                  data-controller="ripple-effect"
                  data-action="click->ripple-effect#click">
            <svg class="h-4 w-4 text-slate-600 group-hover:text-indigo-600 transition-colors duration-300" 
                 fill="none" 
                 stroke="currentColor" 
                 viewBox="0 0 24 24" 
                 stroke-width="2">
              <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
          </button>
        </div>

        <div class="space-y-3" data-sidebar-target="quickActionsList">
          <!-- Data Management Actions -->
          <div class="space-y-2">
            <div class="text-xs font-bold text-slate-500 uppercase tracking-wider pl-2">Data Management</div>
            
            <%= link_to new_data_source_path,
                class: "group flex items-center gap-3 px-3 py-2.5 text-sm text-slate-700 rounded-xl bg-white/30 hover:bg-gradient-to-r hover:from-indigo-50/50 hover:to-purple-50/50 hover:text-indigo-700 transition-all duration-300 border border-transparent hover:border-indigo-200/30",
                data: {
                  controller: "magnetic-button ripple-effect",
                  action: "mousemove->magnetic-button#mouseMove mouseleave->magnetic-button#mouseLeave click->ripple-effect#click"
                } do %>
              <div class="relative">
                <div class="absolute inset-0 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl blur opacity-0 group-hover:opacity-50 transition-opacity duration-300"></div>
                <div class="relative flex h-8 w-8 items-center justify-center rounded-xl bg-gradient-to-br from-indigo-500 to-purple-600 shadow-lg group-hover:shadow-xl transition-all duration-300">
                  <svg class="h-4 w-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 4v16m8-8H4" />
                  </svg>
                </div>
              </div>
              <span class="font-semibold">Add Data Source</span>
              <svg class="h-4 w-4 ml-auto opacity-0 group-hover:opacity-100 -translate-x-2 group-hover:translate-x-0 transition-all duration-300" 
                   fill="none" 
                   stroke="currentColor" 
                   viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
            <% end %>

            <%= link_to analytics_path,
                class: "group flex items-center gap-3 px-3 py-2.5 text-sm text-slate-700 rounded-xl bg-white/30 hover:bg-gradient-to-r hover:from-purple-50/50 hover:to-pink-50/50 hover:text-purple-700 transition-all duration-300 border border-transparent hover:border-purple-200/30",
                data: {
                  controller: "magnetic-button ripple-effect",
                  action: "mousemove->magnetic-button#mouseMove mouseleave->magnetic-button#mouseLeave click->ripple-effect#click"
                } do %>
              <div class="relative">
                <div class="absolute inset-0 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl blur opacity-0 group-hover:opacity-50 transition-opacity duration-300"></div>
                <div class="relative flex h-8 w-8 items-center justify-center rounded-xl bg-gradient-to-br from-purple-500 to-pink-600 shadow-lg group-hover:shadow-xl transition-all duration-300">
                  <svg class="h-4 w-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
              </div>
              <span class="font-semibold">Run Analysis</span>
              <svg class="h-4 w-4 ml-auto opacity-0 group-hover:opacity-100 -translate-x-2 group-hover:translate-x-0 transition-all duration-300" 
                   fill="none" 
                   stroke="currentColor" 
                   viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
            <% end %>
          </div>
        </div>
      </div>
    </div>

    <!-- Premium Navigation Menu -->
    <nav class="flex flex-1 flex-col">
      <ul role="list" class="flex flex-1 flex-col gap-y-7">
        <li>
          <ul role="list" class="-mx-2 space-y-1">
            <% 
              nav_link_classes = ->(section) do
                current_section = case controller_name
                when 'dashboard' then 'dashboard'
                when 'data_sources' then 'data-sources'
                when 'analytics', 'customers', 'products', 'revenue', 'risks' then 'analytics'
                when 'pipeline_dashboard', 'etl_pipeline_builders', 'manual_tasks' then 'pipelines'
                when 'organizations', 'users' then 'settings'
                else nil
                end

                base_classes = "group flex gap-x-3 rounded-xl p-3 text-sm font-semibold leading-6 transition-all duration-300"
                
                if current_section == section
                  "#{base_classes} bg-gradient-to-r from-indigo-500/10 to-purple-500/10 text-indigo-700 shadow-lg border border-indigo-200/30"
                else
                  "#{base_classes} text-slate-700 hover:text-indigo-700 hover:bg-gradient-to-r hover:from-indigo-50/50 hover:to-purple-50/50 hover:shadow-md"
                end
              end
            %>
            
            <!-- Dashboard -->
            <li>
              <%= link_to dashboard_index_path, 
                  class: nav_link_classes.('dashboard'),
                  data: {
                    controller: "magnetic-button ripple-effect",
                    action: "mousemove->magnetic-button#mouseMove mouseleave->magnetic-button#mouseLeave click->ripple-effect#click"
                  } do %>
                <div class="relative">
                  <div class="absolute inset-0 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg blur opacity-0 group-hover:opacity-30 transition-opacity duration-300"></div>
                  <svg class="relative h-6 w-6 shrink-0" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25" />
                  </svg>
                </div>
                <span>Dashboard</span>
                <div class="ml-auto opacity-0 group-hover:opacity-100 transform translate-x-2 group-hover:translate-x-0 transition-all duration-300">
                  <div class="h-2 w-2 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full"></div>
                </div>
              <% end %>
            </li>

            <!-- Data Sources -->
            <li>
              <%= link_to data_sources_path, 
                  class: nav_link_classes.('data-sources'),
                  data: {
                    controller: "magnetic-button ripple-effect",
                    action: "mousemove->magnetic-button#mouseMove mouseleave->magnetic-button#mouseLeave click->ripple-effect#click"
                  } do %>
                <div class="relative">
                  <div class="absolute inset-0 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg blur opacity-0 group-hover:opacity-30 transition-opacity duration-300"></div>
                  <svg class="relative h-6 w-6 shrink-0" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M20.25 6.375c0 2.278-3.694 4.125-8.25 4.125S3.75 8.653 3.75 6.375m16.5 0c0-2.278-3.694-4.125-8.25-4.125S3.75 4.097 3.75 6.375m16.5 0v11.25c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125V6.375m16.5 0v3.75m-16.5-3.75v3.75m16.5 0v3.75C20.25 16.153 16.556 18 12 18s-8.25-1.847-8.25-4.125v-3.75m16.5 0c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125" />
                  </svg>
                </div>
                <span>Data Sources</span>
                <div class="ml-auto flex items-center gap-2">
                  <span class="text-xs px-2 py-0.5 bg-gradient-to-r from-blue-500/10 to-indigo-500/10 text-indigo-700 rounded-full font-medium opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <%= @data_sources&.count || 0 %>
                  </span>
                  <div class="opacity-0 group-hover:opacity-100 transform translate-x-2 group-hover:translate-x-0 transition-all duration-300">
                    <div class="h-2 w-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full"></div>
                  </div>
                </div>
              <% end %>
            </li>

            <!-- Analytics with Submenu -->
            <li data-controller="submenu">
              <button class="<%= nav_link_classes.('analytics') %> w-full"
                      data-action="click->submenu#toggle"
                      data-controller="magnetic-button ripple-effect"
                      data-action="mousemove->magnetic-button#mouseMove mouseleave->magnetic-button#mouseLeave click->ripple-effect#click click->submenu#toggle">
                <div class="relative">
                  <div class="absolute inset-0 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg blur opacity-0 group-hover:opacity-30 transition-opacity duration-300"></div>
                  <svg class="relative h-6 w-6 shrink-0" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 013 19.875v-6.75zM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V8.625zM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V4.125z" />
                  </svg>
                </div>
                <span>Analytics</span>
                <svg class="ml-auto h-5 w-5 shrink-0 transition-transform duration-300" 
                     data-submenu-target="icon"
                     fill="none" 
                     viewBox="0 0 24 24" 
                     stroke-width="1.5" 
                     stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
                </svg>
              </button>
              
              <!-- Premium Submenu -->
              <ul class="mt-2 space-y-1 overflow-hidden transition-all duration-500" 
                  data-submenu-target="menu"
                  style="height: 0;">
                <% [
                  { name: 'Overview', path: analytics_path, icon: 'M3.75 3v11.25A2.25 2.25 0 006 16.5h2.25M3.75 3h-1.5m1.5 0h16.5m0 0h1.5m-1.5 0v11.25A2.25 2.25 0 0118 16.5h-2.25m-7.5 0h7.5m-7.5 0l-1 3m8.5-3l1 3m0 0l.5 1.5m-.5-1.5h-9.5m0 0l-.5 1.5m.75-9l3-3 2.148 2.148A12.061 12.061 0 0116.5 7.605' },
                  { name: 'Customers', path: '#', icon: 'M15 19.128a9.38 9.38 0 002.625.372 9.337 9.337 0 004.121-.952 4.125 4.125 0 00-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 018.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0111.964-3.07M12 6.375a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zm8.25 2.25a2.625 2.625 0 11-5.25 0 2.625 2.625 0 015.25 0z' },
                  { name: 'Products', path: '#', icon: 'M21 7.5l-9-5.25L3 7.5m18 0l-9 5.25m9-5.25v9l-9 5.25M3 7.5l9 5.25M3 7.5v9l9 5.25m0-9v9' },
                  { name: 'Revenue', path: '#', icon: 'M12 6v12m-3-2.818l.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 11-18 0 9 9 0 0118 0z' },
                  { name: 'Risks', path: '#', icon: 'M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z' }
                ].each do |item| %>
                  <li>
                    <%= link_to item[:path], 
                        class: "group flex items-center gap-x-3 rounded-xl p-2 pl-9 text-sm font-medium text-slate-600 hover:text-indigo-700 hover:bg-gradient-to-r hover:from-indigo-50/30 hover:to-purple-50/30 transition-all duration-300",
                        data: {
                          controller: "ripple-effect",
                          action: "click->ripple-effect#click"
                        } do %>
                      <svg class="h-5 w-5 shrink-0" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="<%= item[:icon] %>" />
                      </svg>
                      <span><%= item[:name] %></span>
                    <% end %>
                  </li>
                <% end %>
              </ul>
            </li>

            <!-- Pipelines -->
            <li>
              <%= link_to pipeline_dashboard_path, 
                  class: nav_link_classes.('pipelines'),
                  data: {
                    controller: "magnetic-button ripple-effect",
                    action: "mousemove->magnetic-button#mouseMove mouseleave->magnetic-button#mouseLeave click->ripple-effect#click"
                  } do %>
                <div class="relative">
                  <div class="absolute inset-0 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg blur opacity-0 group-hover:opacity-30 transition-opacity duration-300"></div>
                  <svg class="relative h-6 w-6 shrink-0" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M9.348 14.651a3.75 3.75 0 010-5.303m5.304 0a3.75 3.75 0 010 5.303m-7.425 2.122a6.75 6.75 0 010-9.546m9.546 0a6.75 6.75 0 010 9.546M5.106 18.894c-3.808-3.808-3.808-9.98 0-13.789m13.788 0c3.808 3.808 3.808 9.981 0 13.79M12 12h.008v.007H12V12zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
                  </svg>
                </div>
                <span>Pipelines</span>
                <div class="ml-auto opacity-0 group-hover:opacity-100 transform translate-x-2 group-hover:translate-x-0 transition-all duration-300">
                  <div class="h-2 w-2 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full animate-pulse"></div>
                </div>
              <% end %>
            </li>

            <!-- Settings -->
            <li>
              <%= link_to organization_path, 
                  class: nav_link_classes.('settings'),
                  data: {
                    controller: "magnetic-button ripple-effect",
                    action: "mousemove->magnetic-button#mouseMove mouseleave->magnetic-button#mouseLeave click->ripple-effect#click"
                  } do %>
                <div class="relative">
                  <div class="absolute inset-0 bg-gradient-to-br from-gray-500 to-slate-600 rounded-lg blur opacity-0 group-hover:opacity-30 transition-opacity duration-300"></div>
                  <svg class="relative h-6 w-6 shrink-0" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.*************.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z" />
                    <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                </div>
                <span>Settings</span>
                <div class="ml-auto opacity-0 group-hover:opacity-100 transform translate-x-2 group-hover:translate-x-0 transition-all duration-300">
                  <div class="h-2 w-2 bg-gradient-to-br from-gray-500 to-slate-600 rounded-full"></div>
                </div>
              <% end %>
            </li>
          </ul>
        </li>
      </ul>
    </nav>

    <!-- Premium Bottom Section -->
    <div class="mt-auto space-y-4">
      <!-- Live Activity Feed -->
      <div class="relative bg-white/50 backdrop-blur-sm rounded-2xl p-4 border border-white/20 shadow-xl">
        <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-indigo-500/5 rounded-2xl"></div>
        <div class="relative">
          <div class="flex items-center justify-between mb-3">
            <h4 class="text-xs font-bold text-slate-700 uppercase tracking-wider">Live Activity</h4>
            <button class="p-1 hover:bg-white/50 rounded-lg transition-all duration-300 group"
                    data-action="click->sidebar#refreshActivity"
                    data-sidebar-target="activityRefresh"
                    data-controller="ripple-effect"
                    data-action="click->ripple-effect#click">
              <svg class="h-3 w-3 text-slate-500 group-hover:text-indigo-600 transition-colors duration-300 group-hover:rotate-180" 
                   fill="none" 
                   stroke="currentColor" 
                   viewBox="0 0 24 24" 
                   stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </button>
          </div>
          
          <div class="space-y-2.5" data-sidebar-target="activityFeed">
            <div class="flex items-center gap-3 text-xs group cursor-pointer hover:bg-white/30 rounded-lg p-1.5 transition-all duration-300">
              <div class="relative">
                <div class="absolute inset-0 bg-blue-500 rounded-full blur-sm opacity-50"></div>
                <div class="relative h-2 w-2 bg-blue-500 rounded-full animate-pulse"></div>
              </div>
              <span class="text-slate-700 font-medium truncate flex-1">Shopify sync completed</span>
              <span class="text-slate-500">2m</span>
            </div>
            
            <div class="flex items-center gap-3 text-xs group cursor-pointer hover:bg-white/30 rounded-lg p-1.5 transition-all duration-300">
              <div class="relative">
                <div class="absolute inset-0 bg-green-500 rounded-full blur-sm opacity-50"></div>
                <div class="relative h-2 w-2 bg-green-500 rounded-full"></div>
              </div>
              <span class="text-slate-700 font-medium truncate flex-1">User invited successfully</span>
              <span class="text-slate-500">5m</span>
            </div>
            
            <div class="flex items-center gap-3 text-xs group cursor-pointer hover:bg-white/30 rounded-lg p-1.5 transition-all duration-300">
              <div class="relative">
                <div class="absolute inset-0 bg-purple-500 rounded-full blur-sm opacity-50"></div>
                <div class="relative h-2 w-2 bg-purple-500 rounded-full"></div>
              </div>
              <span class="text-slate-700 font-medium truncate flex-1">Report generated</span>
              <span class="text-slate-500">12m</span>
            </div>
          </div>
          
          <button class="mt-3 text-xs font-semibold text-indigo-600 hover:text-indigo-800 transition-colors duration-300 flex items-center gap-1 group"
                  data-action="click->sidebar#viewAllActivity">
            View all activity
            <svg class="h-3 w-3 group-hover:translate-x-1 transition-transform duration-300" 
                 fill="none" 
                 stroke="currentColor" 
                 viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>
      </div>

      <!-- Premium System Status -->
      <div class="relative bg-white/50 backdrop-blur-sm rounded-2xl p-4 border border-white/20 shadow-xl">
        <div class="absolute inset-0 bg-gradient-to-br from-green-500/5 to-emerald-500/5 rounded-2xl"></div>
        <div class="relative">
          <%
            system_status = @system_status || {
              health: { color: 'gray', text: 'Unknown' },
              uptime: '--',
              processing_jobs: 0,
              storage_used: '-- GB'
            }
            health_color = system_status.dig(:health, :color) || 'gray'
            health_text = system_status.dig(:health, :text) || 'Unknown'
            uptime = system_status[:uptime] || '--'
            processing_jobs = system_status[:processing_jobs] || 0
            storage_used = system_status[:storage_used] || '-- GB'
          %>
          
          <div class="flex items-center justify-between mb-3">
            <h4 class="text-xs font-bold text-slate-700 uppercase tracking-wider">System Health</h4>
            <div class="flex items-center gap-2">
              <div class="relative">
                <div class="absolute inset-0 bg-<%= health_color %>-500 rounded-full blur-sm opacity-50 animate-pulse"></div>
                <div class="relative h-2 w-2 bg-<%= health_color %>-500 rounded-full animate-pulse"></div>
              </div>
              <span class="text-xs font-semibold text-<%= health_color %>-600"><%= health_text %></span>
            </div>
          </div>
          
          <div class="space-y-2.5">
            <div class="flex items-center justify-between">
              <span class="text-xs text-slate-600 font-medium">API Uptime</span>
              <span class="text-xs font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                <%= uptime %>
              </span>
            </div>
            
            <div class="flex items-center justify-between">
              <span class="text-xs text-slate-600 font-medium">Processing</span>
              <span class="text-xs font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                <%= pluralize(processing_jobs, 'job') %>
              </span>
            </div>
            
            <div class="flex items-center justify-between">
              <span class="text-xs text-slate-600 font-medium">Storage</span>
              <span class="text-xs font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                <%= storage_used %>
              </span>
            </div>
          </div>
          
          <button class="mt-3 text-xs font-semibold text-indigo-600 hover:text-indigo-800 transition-colors duration-300 flex items-center gap-1 group"
                  data-action="click->sidebar#showSystemStatus">
            System details
            <svg class="h-3 w-3 group-hover:translate-x-1 transition-transform duration-300" 
                 fill="none" 
                 stroke="currentColor" 
                 viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>
      </div>

      <!-- Premium User Profile -->
      <div class="relative bg-white/50 backdrop-blur-sm rounded-2xl p-4 border border-white/20 shadow-xl">
        <div class="absolute inset-0 bg-gradient-to-br from-indigo-500/5 to-purple-500/5 rounded-2xl"></div>
        <div class="relative flex items-center gap-3">
          <div class="relative">
            <div class="absolute inset-0 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full blur opacity-50"></div>
            <% if current_user&.avatar&.attached? %>
              <%= image_tag current_user.avatar, 
                  class: "relative h-10 w-10 rounded-full ring-2 ring-white/50 shadow-xl" %>
            <% else %>
              <div class="relative h-10 w-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center ring-2 ring-white/50 shadow-xl">
                <span class="text-white font-bold text-sm">
                  <%= current_user&.name&.first || 'U' %>
                </span>
              </div>
            <% end %>
          </div>
          
          <div class="flex-1">
            <p class="text-sm font-semibold text-slate-900">
              <%= current_user&.name || 'User' %>
            </p>
            <p class="text-xs text-slate-500">
              <%= current_user&.email %>
            </p>
          </div>
          
          <%= link_to destroy_user_session_path, 
              method: :delete,
              class: "p-2 hover:bg-white/50 rounded-lg transition-all duration-300 group",
              data: {
                controller: "ripple-effect",
                action: "click->ripple-effect#click"
              } do %>
            <svg class="h-5 w-5 text-slate-500 group-hover:text-red-600 transition-colors duration-300" 
                 fill="none" 
                 viewBox="0 0 24 24" 
                 stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
            </svg>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Premium Mobile Navigation -->
<div class="lg:hidden" data-controller="mobile-menu">
  <!-- Mobile menu button -->
  <div class="fixed top-0 left-0 right-0 z-50 flex items-center justify-between bg-white/80 backdrop-blur-xl border-b border-white/20 px-4 py-3 shadow-xl">
    <div class="flex items-center gap-3">
      <button type="button" 
              class="p-2 rounded-xl text-slate-700 hover:bg-white/50 transition-all duration-300"
              data-action="click->mobile-menu#toggle"
              data-controller="ripple-effect"
              data-action="click->ripple-effect#click">
        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
        </svg>
      </button>
      
      <%= link_to root_path, class: "flex items-center gap-2" do %>
        <div class="flex h-8 w-8 items-center justify-center rounded-xl bg-gradient-to-br from-indigo-600 to-purple-700 shadow-lg">
          <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
          </svg>
        </div>
        <span class="text-lg font-bold bg-gradient-to-r from-gray-900 to-indigo-900 bg-clip-text text-transparent">
          DataReflow
        </span>
      <% end %>
    </div>
  </div>
  
  <!-- Mobile menu overlay -->
  <div class="fixed inset-0 z-40 hidden" 
       data-mobile-menu-target="overlay"
       data-action="click->mobile-menu#close">
    <div class="fixed inset-0 bg-black/50 backdrop-blur-sm"></div>
  </div>
  
  <!-- Mobile menu panel -->
  <div class="fixed inset-y-0 left-0 z-50 w-80 -translate-x-full transition-transform duration-300 ease-out" 
       data-mobile-menu-target="panel">
    <div class="flex h-full flex-col bg-white/95 backdrop-blur-xl shadow-2xl">
      <!-- Mobile menu content similar to desktop but adapted for mobile -->
      <div class="flex-1 overflow-y-auto px-4 py-6">
        <!-- Add mobile navigation items here -->
      </div>
    </div>
  </div>
</div>