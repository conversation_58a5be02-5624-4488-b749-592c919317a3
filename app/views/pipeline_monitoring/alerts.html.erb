<% content_for :page_title, "Pipeline Alerts - DataReflow" %>

<!-- Premium Pipeline Alerts Dashboard -->
<div class="min-h-screen relative overflow-hidden"
     style="background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);"
     role="main"
     aria-label="Pipeline Alerts Dashboard">
  
  <!-- Premium Background Elements -->
  <div class="absolute inset-0 overflow-hidden pointer-events-none">
    <div class="absolute top-20 left-20 w-96 h-96 bg-gradient-to-r from-red-400/10 to-orange-400/10 rounded-full mix-blend-multiply filter blur-3xl animate-pulse"></div>
    <div class="absolute bottom-20 right-20 w-80 h-80 bg-gradient-to-r from-amber-400/10 to-yellow-400/10 rounded-full mix-blend-multiply filter blur-3xl animate-pulse" style="animation-delay: 2s;"></div>
  </div>

  <!-- Premium Page Header -->
  <div class="relative z-10 premium-mb-12">
    <div class="premium-container">
      <div class="premium-card premium-card-glass p-8" 
           style="background: rgba(255, 255, 255, 0.8); backdrop-filter: blur(20px); border: 1px solid rgba(255, 255, 255, 0.3);">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
          <div class="flex-1">
            <div class="flex items-center gap-4 premium-mb-4">
              <div class="flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-br from-red-500 to-orange-600 shadow-xl">
                <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
              </div>
              <div>
                <h1 class="premium-heading-1 text-gray-900" role="heading" aria-level="1">
                  Pipeline Alerts
                </h1>
                <p class="premium-body-large text-gray-600 mt-2">
                  Monitor and manage pipeline alerts and system notifications
                </p>
              </div>
            </div>
            
            <!-- Alert Summary -->
            <div class="flex items-center gap-6">
              <% 
                total_alerts = @alerts.respond_to?(:total_count) ? @alerts.total_count : @alerts.count
                critical_count = @alerts.select { |a| a.severity == 'critical' }.count
                high_count = @alerts.select { |a| a.severity == 'high' }.count
                active_count = @alerts.select { |a| a.status == 'active' }.count
              %>
              <div class="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-red-50 to-orange-50 rounded-full border border-red-200">
                <div class="w-3 h-3 bg-gradient-to-r from-red-400 to-orange-400 rounded-full <%= critical_count > 0 ? 'animate-pulse' : '' %>"></div>
                <span class="premium-body-small text-red-700 font-medium">
                  <%= pluralize(critical_count, 'Critical Alert') %>
                </span>
              </div>
              <div class="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-amber-50 to-yellow-50 rounded-full border border-amber-200">
                <div class="w-3 h-3 bg-gradient-to-r from-amber-400 to-yellow-400 rounded-full <%= high_count > 0 ? 'animate-pulse' : '' %>"></div>
                <span class="premium-body-small text-amber-700 font-medium">
                  <%= pluralize(high_count, 'High Priority') %>
                </span>
              </div>
              <div class="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-full border border-blue-200">
                <div class="w-3 h-3 bg-gradient-to-r from-blue-400 to-indigo-400 rounded-full <%= active_count > 0 ? 'animate-pulse' : '' %>"></div>
                <span class="premium-body-small text-blue-700 font-medium">
                  <%= pluralize(active_count, 'Active Alert') %>
                </span>
              </div>
            </div>
          </div>
          
          <!-- Action Buttons -->
          <div class="flex flex-col sm:flex-row gap-4">
            <%= link_to pipeline_monitoring_index_path, 
                class: "premium-btn premium-btn-secondary group" do %>
              <svg class="w-5 h-5 mr-3 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
              Pipeline Dashboard
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Premium Alerts List -->
  <div class="relative z-10 premium-mb-16">
    <div class="premium-container">
      <% if @alerts.any? %>
        <div class="space-y-6">
          <% @alerts.each_with_index do |alert, index| %>
            <div class="premium-card premium-card-glass group hover:scale-[1.02] transition-all duration-500" 
                 style="background: rgba(255, 255, 255, 0.7); backdrop-filter: blur(15px); border: 1px solid rgba(255, 255, 255, 0.2); animation-delay: <%= index * 0.1 %>s;"
                 data-controller="alert-card"
                 data-alert-id="<%= alert.id %>"
                 role="article"
                 aria-labelledby="alert-title-<%= alert.id %>">
              
              <!-- Alert Header -->
              <div class="flex items-start justify-between p-6 border-b border-gray-100/50">
                <div class="flex items-start gap-4 flex-1">
                  <!-- Severity Indicator -->
                  <div class="flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-br 
                              <%= case alert.severity
                                  when 'critical' then 'from-red-500 to-red-600 shadow-red-500/25'
                                  when 'high' then 'from-orange-500 to-amber-600 shadow-orange-500/25'
                                  when 'medium' then 'from-yellow-500 to-yellow-600 shadow-yellow-500/25'
                                  when 'low' then 'from-blue-500 to-blue-600 shadow-blue-500/25'
                                  else 'from-gray-500 to-gray-600 shadow-gray-500/25'
                                  end %> shadow-xl group-hover:scale-110 transition-all duration-300">
                    <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                      <%= case alert.severity
                          when 'critical' %>
                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                          <% when 'high' %>
                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                          <% when 'medium' %>
                            <path stroke-linecap="round" stroke-linejoin="round" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                          <% when 'low' %>
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                          <% else %>
                            <path stroke-linecap="round" stroke-linejoin="round" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
                          <% end %>
                    </svg>
                  </div>
                  
                  <!-- Alert Content -->
                  <div class="flex-1 min-w-0">
                    <div class="flex items-center gap-3 premium-mb-2">
                      <h3 id="alert-title-<%= alert.id %>" class="premium-heading-3 text-gray-900 truncate">
                        <%= alert.title %>
                      </h3>
                      <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium
                                   <%= case alert.severity
                                       when 'critical' then 'bg-red-100 text-red-800 border border-red-200'
                                       when 'high' then 'bg-orange-100 text-orange-800 border border-orange-200'
                                       when 'medium' then 'bg-yellow-100 text-yellow-800 border border-yellow-200'
                                       when 'low' then 'bg-blue-100 text-blue-800 border border-blue-200'
                                       else 'bg-gray-100 text-gray-800 border border-gray-200'
                                       end %>">
                        <%= alert.severity.capitalize %>
                      </span>
                      <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium
                                   <%= case alert.status
                                       when 'active' then 'bg-red-100 text-red-800 border border-red-200'
                                       when 'acknowledged' then 'bg-yellow-100 text-yellow-800 border border-yellow-200'
                                       when 'resolved' then 'bg-green-100 text-green-800 border border-green-200'
                                       when 'dismissed' then 'bg-gray-100 text-gray-800 border border-gray-200'
                                       else 'bg-gray-100 text-gray-800 border border-gray-200'
                                       end %>">
                        <%= alert.status.capitalize %>
                      </span>
                    </div>
                    <p class="premium-body text-gray-600 premium-mb-3">
                      <%= alert.message %>
                    </p>
                    <div class="flex items-center gap-4 text-sm text-gray-500">
                      <div class="flex items-center gap-2">
                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                          <path stroke-linecap="round" stroke-linejoin="round" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span><%= time_ago_in_words(alert.created_at) %> ago</span>
                      </div>
                      <% if alert.data_source %>
                        <div class="flex items-center gap-2">
                          <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path>
                          </svg>
                          <span><%= alert.data_source.name %></span>
                        </div>
                      <% end %>
                      <% if alert.user %>
                        <div class="flex items-center gap-2">
                          <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                          </svg>
                          <span><%= alert.user.full_name %></span>
                        </div>
                      <% end %>
                    </div>
                  </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="flex items-center gap-2 ml-4">
                  <% unless alert.resolved? %>
                    <% unless alert.acknowledged? %>
                      <button class="premium-btn premium-btn-sm bg-yellow-500 hover:bg-yellow-600 text-white"
                              data-action="click->alert-card#acknowledge"
                              title="Acknowledge Alert">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                          <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                      </button>
                    <% end %>
                    <button class="premium-btn premium-btn-sm bg-green-500 hover:bg-green-600 text-white"
                            data-action="click->alert-card#resolve"
                            title="Resolve Alert">
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path>
                      </svg>
                    </button>
                  <% end %>
                  <button class="premium-btn premium-btn-sm bg-gray-500 hover:bg-gray-600 text-white"
                          data-action="click->alert-card#dismiss"
                          title="Dismiss Alert">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                  </button>
                </div>
              </div>
              
              <!-- Premium Hover Effect -->
              <div class="absolute inset-0 bg-gradient-to-r 
                          <%= case alert.severity
                              when 'critical' then 'from-red-500/5 to-red-600/5'
                              when 'high' then 'from-orange-500/5 to-amber-600/5'
                              when 'medium' then 'from-yellow-500/5 to-yellow-600/5'
                              when 'low' then 'from-blue-500/5 to-blue-600/5'
                              else 'from-gray-500/5 to-gray-600/5'
                              end %> opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl"></div>
            </div>
          <% end %>
        </div>
        
        <!-- Pagination -->
        <% if @alerts.respond_to?(:total_pages) && @alerts.total_pages > 1 %>
          <div class="premium-mt-12 flex justify-center">
            <div class="premium-card premium-card-glass p-4" 
                 style="background: rgba(255, 255, 255, 0.7); backdrop-filter: blur(15px);">
              <%= paginate @alerts, theme: 'premium' %>
            </div>
          </div>
        <% end %>
        
      <% else %>
        <!-- Empty State -->
        <div class="premium-card premium-card-glass text-center p-12" 
             style="background: rgba(255, 255, 255, 0.7); backdrop-filter: blur(15px);">
          <div class="mx-auto w-24 h-24 bg-gradient-to-br from-green-100 to-emerald-200 rounded-full flex items-center justify-center premium-mb-6">
            <svg class="h-12 w-12 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
              <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <h3 class="premium-heading-2 text-gray-900 premium-mb-4">No Pipeline Alerts</h3>
          <p class="premium-body-large text-gray-600 premium-mb-8 max-w-md mx-auto">
            Great news! There are currently no pipeline alerts requiring your attention. 
            Your data pipelines are running smoothly.
          </p>
          <%= link_to pipeline_monitoring_index_path, 
              class: "premium-btn premium-btn-primary" do %>
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
              <path stroke-linecap="round" stroke-linejoin="round" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
            View Pipeline Dashboard
          <% end %>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Live Update Indicator -->
  <div class="fixed bottom-6 right-6 z-50">
    <div class="flex items-center space-x-2 px-4 py-2 bg-white/90 backdrop-blur-xl rounded-full shadow-xl border border-white/20">
      <div class="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
      <span class="text-xs font-semibold text-slate-700">Live Alerts</span>
    </div>
  </div>
</div>
