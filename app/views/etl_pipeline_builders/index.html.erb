<div class="min-h-screen bg-gray-50">
  <!-- <PERSON> Header -->
  <div class="bg-white shadow-sm border-b border-gray-200">
    <div class="px-4 sm:px-6 lg:px-8">
      <div class="py-6">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-3xl font-bold text-gray-900">ETL/ELT Pipelines</h1>
            <p class="mt-2 text-sm text-gray-600">Design, manage, and monitor your data pipelines</p>
          </div>
          <div class="flex space-x-3">
            <%= link_to new_etl_pipeline_builder_path, class: "inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
              <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
              </svg>
              New Pipeline
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Quick Stats -->
  <div class="px-4 sm:px-6 lg:px-8 py-8">
    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-6 w-6 text-green-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Active Pipelines</dt>
                <dd class="flex items-baseline">
                  <div class="text-2xl font-semibold text-gray-900">
                    <%= @pipelines.active.count %>
                  </div>
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Scheduled</dt>
                <dd class="flex items-baseline">
                  <div class="text-2xl font-semibold text-gray-900">
                    <%= @pipelines.scheduled.count %>
                  </div>
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-6 w-6 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z" />
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Recent Executions</dt>
                <dd class="flex items-baseline">
                  <div class="text-2xl font-semibold text-gray-900">
                    <%= @recent_executions.count %>
                  </div>
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-6 w-6 text-indigo-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Templates</dt>
                <dd class="flex items-baseline">
                  <div class="text-2xl font-semibold text-gray-900">
                    <%= @pipeline_templates.count %>
                  </div>
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Pipeline Templates -->
  <div class="px-4 sm:px-6 lg:px-8 pb-8">
    <div class="bg-white shadow rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200">
        <h2 class="text-lg font-medium text-gray-900">Quick Start Templates</h2>
      </div>
      <div class="p-6">
        <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
          <% (@pipeline_templates || []).each do |template| %>
            <div class="relative rounded-lg border border-gray-300 bg-white px-6 py-5 shadow-sm hover:border-gray-400 focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-indigo-500">
              <div>
                <span class="inline-flex rounded-lg p-3 
                  <%= case template[:config][:pipeline_type]
                      when 'etl' then 'bg-green-50 text-green-700'
                      when 'elt' then 'bg-blue-50 text-blue-700'
                      when 'streaming' then 'bg-purple-50 text-purple-700'
                      else 'bg-gray-50 text-gray-700'
                      end %>">
                  <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M20.25 6.375c0 2.278-3.694 4.125-8.25 4.125S3.75 8.653 3.75 6.375m16.5 0c0-2.278-3.694-4.125-8.25-4.125S3.75 4.097 3.75 6.375m16.5 0v11.25c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125V6.375m16.5 0v3.75m-16.5-3.75v3.75m16.5 0v3.75C20.25 16.153 16.556 18 12 18s-8.25-1.847-8.25-4.125v-3.75m16.5 0c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125" />
                  </svg>
                </span>
              </div>
              <div class="mt-4">
                <h3 class="text-lg font-medium">
                  <%= link_to template[:name], new_etl_pipeline_builder_path(template: template[:name]), class: "focus:outline-none" %>
                  <span class="absolute inset-0" aria-hidden="true"></span>
                </h3>
                <p class="mt-2 text-sm text-gray-500">
                  <%= template[:description] %>
                </p>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <!-- Pipelines List -->
  <div class="px-4 sm:px-6 lg:px-8 pb-8">
    <div class="bg-white shadow rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-medium text-gray-900">Your Pipelines</h2>
          <div class="flex space-x-2">
            <select class="rounded-md border-gray-300 py-2 pl-3 pr-10 text-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500">
              <option>All Types</option>
              <option>ETL</option>
              <option>ELT</option>
              <option>Streaming</option>
            </select>
            <select class="rounded-md border-gray-300 py-2 pl-3 pr-10 text-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500">
              <option>All Status</option>
              <option>Active</option>
              <option>Paused</option>
              <option>Draft</option>
            </select>
          </div>
        </div>
      </div>

      <div class="overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Pipeline
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Type
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Schedule
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Last Run
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Success Rate
              </th>
              <th scope="col" class="relative px-6 py-3">
                <span class="sr-only">Actions</span>
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <% (@pipelines || []).each do |pipeline| %>
              <tr class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div>
                      <div class="text-sm font-medium text-gray-900">
                        <%= link_to pipeline.name, etl_pipeline_builder_path(pipeline), class: "hover:text-indigo-600" %>
                      </div>
                      <div class="text-sm text-gray-500">
                        <%= truncate(pipeline.description, length: 50) if pipeline.description %>
                      </div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                    <%= case pipeline.pipeline_type
                        when 'etl' then 'bg-green-100 text-green-800'
                        when 'elt' then 'bg-blue-100 text-blue-800'
                        when 'streaming' then 'bg-purple-100 text-purple-800'
                        else 'bg-gray-100 text-gray-800'
                        end %>">
                    <%= pipeline.pipeline_type.upcase %>
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                    <%= case pipeline.status
                        when 'active' then 'bg-green-100 text-green-800'
                        when 'paused' then 'bg-yellow-100 text-yellow-800'
                        when 'draft' then 'bg-gray-100 text-gray-800'
                        when 'archived' then 'bg-red-100 text-red-800'
                        end %>">
                    <%= pipeline.status.capitalize %>
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <% if pipeline.scheduled? %>
                    <div class="flex items-center">
                      <svg class="mr-1.5 h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <%= pipeline.schedule_config['type'] %>
                    </div>
                  <% else %>
                    <span class="text-gray-400">Manual</span>
                  <% end %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <% if pipeline.last_executed_at %>
                    <div>
                      <div><%= time_ago_in_words(pipeline.last_executed_at) %> ago</div>
                      <div class="text-xs text-gray-400">by <%= pipeline.last_executed_by&.email %></div>
                    </div>
                  <% else %>
                    <span class="text-gray-400">Never</span>
                  <% end %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <% stats = pipeline.execution_stats(7.days) %>
                  <div class="flex items-center">
                    <div class="flex-1">
                      <div class="flex items-center">
                        <span class="text-sm font-medium text-gray-900"><%= stats[:success_rate] %>%</span>
                        <span class="ml-2 text-xs text-gray-500">(<%= stats[:total_runs] %> runs)</span>
                      </div>
                      <div class="mt-1 w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-green-600 h-2 rounded-full" style="width: <%= stats[:success_rate] %>%"></div>
                      </div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div class="flex items-center justify-end space-x-2">
                    <%= link_to "View", etl_pipeline_builder_path(pipeline), class: "text-indigo-600 hover:text-indigo-900" %>
                    <%= link_to "Edit", edit_etl_pipeline_builder_path(pipeline), class: "text-indigo-600 hover:text-indigo-900" %>
                    <% if pipeline.active? %>
                      <%= button_to "Run", execute_etl_pipeline_builder_path(pipeline), method: :post, class: "text-green-600 hover:text-green-900" %>
                    <% end %>
                  </div>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>

      <% if @pipelines.any? %>
        <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
          <%= paginate @pipelines %>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Recent Executions -->
  <% if @recent_executions.any? %>
    <div class="px-4 sm:px-6 lg:px-8 pb-8">
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
          <h2 class="text-lg font-medium text-gray-900">Recent Executions</h2>
        </div>
        <div class="overflow-hidden">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Pipeline
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Started
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Duration
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Progress
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <% @recent_executions.each do |execution| %>
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    <%= execution.pipeline_name %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <%= execution.started_at.strftime("%b %d, %I:%M %p") %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <%= execution.duration_seconds ? "#{execution.duration_seconds}s" : "-" %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                      <%= case execution.status
                          when 'successful' then 'bg-green-100 text-green-800'
                          when 'failed' then 'bg-red-100 text-red-800'
                          when 'running' then 'bg-blue-100 text-blue-800'
                          else 'bg-gray-100 text-gray-800'
                          end %>">
                      <%= execution.status %>
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <%= execution.progress ? "#{execution.progress}%" : "-" %>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  <% end %>
</div>