<% content_for :page_title, "Dashboard" %>

<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
  <!-- Premium Header -->
  <div class="relative bg-white/80 backdrop-blur-xl border-b border-white/20 shadow-xl">
    <div class="absolute inset-0 bg-gradient-to-r from-blue-600/10 to-indigo-600/10"></div>
    <div class="relative px-4 sm:px-6 lg:px-8 py-8">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div class="space-y-2">
          <div class="flex items-center space-x-3">
            <div class="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg">
              <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
              </svg>
            </div>
            <div>
              <h1 class="text-3xl font-bold bg-gradient-to-r from-gray-900 via-blue-900 to-indigo-900 bg-clip-text text-transparent">
                Welcome back, <%= current_user.first_name %>
              </h1>
              <p class="text-slate-600 font-medium">Here's an overview of your data pipeline</p>
            </div>
          </div>
        </div>
        <div class="mt-4 sm:mt-0 flex items-center space-x-4">
          <!-- Live Status Indicator -->
          <div class="flex items-center space-x-2 px-4 py-2 bg-green-100/80 backdrop-blur-sm rounded-full border border-green-200/50">
            <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span class="text-green-700 text-sm font-semibold">System Online</span>
          </div>
          <!-- Add Data Source Button -->
          <%= link_to new_data_source_path, class: "group inline-flex items-center px-6 py-3 border border-slate-300/50 rounded-xl shadow-lg text-sm font-semibold text-slate-700 bg-white/70 backdrop-blur-sm hover:bg-white/90 hover:shadow-xl transition-all duration-300 hover:-translate-y-0.5" do %>
            <svg class="h-4 w-4 mr-2 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
            </svg>
            Add Data Source
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <!-- Key Metrics - Simplified -->
  <div class="px-4 sm:px-6 lg:px-8 py-8">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <!-- Data Sources -->
      <div class="group relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl hover:-translate-y-1 transition-all duration-500">
        <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-indigo-500/5 rounded-2xl"></div>
        <div class="relative">
          <div class="flex items-center justify-between mb-4">
            <div class="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg group-hover:shadow-blue-500/25 transition-all duration-300">
              <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path>
              </svg>
            </div>
            <div class="text-right">
              <div class="text-2xl font-bold text-slate-900"><%= @stats[:total_data_sources] %></div>
              <div class="text-xs text-slate-500">sources</div>
            </div>
          </div>
          <div>
            <h3 class="text-sm font-semibold text-slate-600 uppercase tracking-wider">Data Sources</h3>
            <div class="mt-2 flex items-center space-x-2">
              <div class="flex-1 bg-slate-200 rounded-full h-2">
                <div class="bg-gradient-to-r from-blue-500 to-indigo-600 h-2 rounded-full transition-all duration-1000" style="width: <%= @stats[:total_data_sources] > 0 ? (@stats[:connected_sources].to_f / @stats[:total_data_sources] * 100).round : 0 %>%"></div>
              </div>
              <span class="text-xs text-slate-500"><%= @stats[:connected_sources] %> active</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Total Records -->
      <div class="group relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl hover:-translate-y-1 transition-all duration-500">
        <div class="absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-green-500/5 rounded-2xl"></div>
        <div class="relative">
          <div class="flex items-center justify-between mb-4">
            <div class="p-3 bg-gradient-to-br from-emerald-500 to-green-600 rounded-xl shadow-lg group-hover:shadow-emerald-500/25 transition-all duration-300">
              <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
            </div>
            <div class="text-right">
              <div class="text-2xl font-bold text-slate-900"><%= number_with_delimiter(@stats[:total_records], delimiter: ",") %></div>
              <div class="text-xs text-slate-500">records</div>
            </div>
          </div>
          <div>
            <h3 class="text-sm font-semibold text-slate-600 uppercase tracking-wider">Records Processed</h3>
            <div class="mt-2 flex items-center space-x-2">
              <div class="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"></div>
              <span class="text-xs text-slate-500">+8.2% this month</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Pipeline Health -->
      <div class="group relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl hover:-translate-y-1 transition-all duration-500">
        <div class="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-indigo-500/5 rounded-2xl"></div>
        <div class="relative">
          <div class="flex items-center justify-between mb-4">
            <div class="p-3 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl shadow-lg group-hover:shadow-purple-500/25 transition-all duration-300">
              <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div class="text-right">
              <div class="text-2xl font-bold text-slate-900">98%</div>
              <div class="text-xs text-slate-500">health</div>
            </div>
          </div>
          <div>
            <h3 class="text-sm font-semibold text-slate-600 uppercase tracking-wider">Pipeline Health</h3>
            <div class="mt-2 flex items-center space-x-2">
              <div class="w-2 h-2 bg-green-500 rounded-full"></div>
              <span class="text-xs text-green-600 font-medium">Excellent</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Last Sync -->
      <div class="group relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl hover:-translate-y-1 transition-all duration-500">
        <div class="absolute inset-0 bg-gradient-to-br from-orange-500/5 to-red-500/5 rounded-2xl"></div>
        <div class="relative">
          <div class="flex items-center justify-between mb-4">
            <div class="p-3 bg-gradient-to-br from-orange-500 to-red-600 rounded-xl shadow-lg group-hover:shadow-orange-500/25 transition-all duration-300">
              <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div class="text-right">
              <div class="text-xl font-bold text-slate-900">
                <%= @stats[:last_sync] ? time_ago_in_words(@stats[:last_sync]) : "Never" %>
              </div>
              <div class="text-xs text-slate-500">ago</div>
            </div>
          </div>
          <div>
            <h3 class="text-sm font-semibold text-slate-600 uppercase tracking-wider">Last Sync</h3>
            <div class="mt-2 flex items-center space-x-2">
              <div class="w-2 h-2 <%= @stats[:last_sync] ? 'bg-green-500' : 'bg-gray-400' %> rounded-full <%= @stats[:last_sync] ? 'animate-pulse' : '' %>"></div>
              <span class="text-xs <%= @stats[:last_sync] ? 'text-green-600' : 'text-gray-500' %> font-medium">
                <%= @stats[:last_sync] ? 'Active' : 'Inactive' %>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content - Two Column Layout -->
  <div class="px-4 sm:px-6 lg:px-8 pb-8">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Data Sources - Takes 2/3 width -->
      <div class="lg:col-span-2">
        <div class="relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 overflow-hidden hover:shadow-2xl transition-all duration-500">
          <div class="absolute inset-0 bg-gradient-to-br from-slate-500/5 to-gray-500/5"></div>
          <div class="relative">
            <div class="px-6 py-5 border-b border-slate-200/50 bg-gradient-to-r from-slate-50/50 to-gray-50/50">
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                  <div class="p-2 bg-gradient-to-br from-slate-600 to-gray-700 rounded-lg shadow-lg">
                    <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path>
                    </svg>
                  </div>
                  <div>
                    <h3 class="text-lg font-bold text-slate-900">Data Sources</h3>
                    <p class="text-sm text-slate-600">Your connected integrations</p>
                  </div>
                </div>
                <div class="flex items-center space-x-3">
                  <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-blue-100/80 text-blue-800">
                    <%= @data_sources.count %> Active
                  </span>
                  <%= link_to data_sources_path, class: "text-sm font-semibold text-indigo-600 hover:text-indigo-800 transition-colors" do %>
                    View all →
                  <% end %>
                </div>
              </div>
            </div>
            <div class="p-6">
              <% if @data_sources.any? %>
                <div class="space-y-4">
                  <% @data_sources.first(4).each do |data_source| %>
                    <div class="group flex items-center justify-between p-4 bg-gradient-to-r from-slate-50/50 to-white/50 rounded-xl border border-slate-200/50 hover:shadow-md transition-all duration-300">
                      <div class="flex items-center space-x-4">
                        <div class="relative">
                          <div class="h-12 w-12 rounded-xl <%= case data_source.source_type
                            when 'shopify' then 'bg-green-100 text-green-600'
                            when 'stripe' then 'bg-purple-100 text-purple-600'
                            when 'google_analytics' then 'bg-orange-100 text-orange-600'
                            when 'quickbooks' then 'bg-blue-100 text-blue-600'
                            when 'mailchimp' then 'bg-yellow-100 text-yellow-600'
                            else 'bg-gray-100 text-gray-600'
                            end %> flex items-center justify-center font-semibold shadow-lg">
                            <%= data_source.source_type.first(2).upcase %>
                          </div>
                          <% if data_source.status == 'connected' %>
                            <div class="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white flex items-center justify-center">
                              <div class="w-1.5 h-1.5 bg-white rounded-full"></div>
                            </div>
                          <% end %>
                        </div>
                        <div>
                          <h4 class="text-sm font-semibold text-slate-900"><%= data_source.name %></h4>
                          <p class="text-sm text-slate-500"><%= data_source.source_type.humanize %></p>
                        </div>
                      </div>
                      <div class="flex items-center space-x-3">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold <%= case data_source.status
                          when 'connected' then 'bg-green-100 text-green-800'
                          when 'syncing' then 'bg-blue-100 text-blue-800'
                          when 'error' then 'bg-red-100 text-red-800'
                          else 'bg-gray-100 text-gray-800'
                          end %>">
                          <%= data_source.status.humanize %>
                        </span>
                        <%= link_to data_source_path(data_source), class: "group/link p-2 text-indigo-600 hover:text-indigo-800 bg-indigo-50/50 hover:bg-indigo-100/80 rounded-lg transition-all duration-300 hover:shadow-md" do %>
                          <svg class="h-4 w-4 group-hover/link:translate-x-0.5 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                          </svg>
                        <% end %>
                      </div>
                    </div>
                  <% end %>
                </div>
              <% else %>
                <div class="text-center py-12">
                  <div class="mx-auto w-16 h-16 bg-gradient-to-br from-slate-100 to-slate-200 rounded-full flex items-center justify-center mb-4">
                    <svg class="h-8 w-8 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path>
                    </svg>
                  </div>
                  <h3 class="text-lg font-semibold text-slate-900 mb-2">No Data Sources</h3>
                  <p class="text-slate-500 mb-6">Connect your first data source to get started</p>
                  <%= link_to new_data_source_path, class: "inline-flex items-center px-6 py-3 bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-0.5" do %>
                    <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                    Add Data Source
                  <% end %>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Actions & Activity - Takes 1/3 width -->
      <div class="space-y-8">
        <!-- Quick Actions -->
        <div class="relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 overflow-hidden hover:shadow-2xl transition-all duration-500">
          <div class="absolute inset-0 bg-gradient-to-br from-indigo-500/5 to-purple-500/5"></div>
          <div class="relative">
            <div class="px-6 py-5 border-b border-slate-200/50 bg-gradient-to-r from-indigo-50/50 to-purple-50/50">
              <div class="flex items-center space-x-3">
                <div class="p-2 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg shadow-lg">
                  <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <div>
                  <h3 class="text-lg font-bold text-slate-900">Quick Actions</h3>
                  <p class="text-sm text-slate-600">Common tasks</p>
                </div>
              </div>
            </div>
            <div class="p-6">
              <div class="space-y-3">
                <%= link_to new_data_source_path, class: "group flex items-center justify-between p-3 bg-gradient-to-r from-blue-50/50 to-indigo-50/50 rounded-xl hover:shadow-md transition-all duration-300" do %>
                  <div class="flex items-center space-x-3">
                    <div class="p-2 bg-blue-100 rounded-lg">
                      <svg class="h-4 w-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                      </svg>
                    </div>
                    <span class="text-sm font-medium text-slate-900">Add Data Source</span>
                  </div>
                  <svg class="h-4 w-4 text-slate-400 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                <% end %>

                <%= link_to analytics_path, class: "group flex items-center justify-between p-3 bg-gradient-to-r from-emerald-50/50 to-green-50/50 rounded-xl hover:shadow-md transition-all duration-300" do %>
                  <div class="flex items-center space-x-3">
                    <div class="p-2 bg-emerald-100 rounded-lg">
                      <svg class="h-4 w-4 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                      </svg>
                    </div>
                    <span class="text-sm font-medium text-slate-900">View Analytics</span>
                  </div>
                  <svg class="h-4 w-4 text-slate-400 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                <% end %>

                <%= link_to pipeline_monitoring_index_path, class: "group flex items-center justify-between p-3 bg-gradient-to-r from-purple-50/50 to-indigo-50/50 rounded-xl hover:shadow-md transition-all duration-300" do %>
                  <div class="flex items-center space-x-3">
                    <div class="p-2 bg-purple-100 rounded-lg">
                      <svg class="h-4 w-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                      </svg>
                    </div>
                    <span class="text-sm font-medium text-slate-900">Monitor Pipelines</span>
                  </div>
                  <svg class="h-4 w-4 text-slate-400 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                <% end %>
              </div>
            </div>
          </div>
        </div>

        <!-- Recent Activity -->
        <div class="relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 overflow-hidden hover:shadow-2xl transition-all duration-500">
          <div class="absolute inset-0 bg-gradient-to-br from-orange-500/5 to-red-500/5"></div>
          <div class="relative">
            <div class="px-6 py-5 border-b border-slate-200/50 bg-gradient-to-r from-orange-50/50 to-red-50/50">
              <div class="flex items-center space-x-3">
                <div class="p-2 bg-gradient-to-br from-orange-500 to-red-600 rounded-lg shadow-lg">
                  <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <h3 class="text-lg font-bold text-slate-900">Activity</h3>
                  <p class="text-sm text-slate-600">Recent updates</p>
                </div>
              </div>
            </div>
            <div class="p-6">
              <% if @recent_activity.any? %>
                <div class="space-y-4">
                  <% @recent_activity.first(4).each do |activity| %>
                    <div class="flex items-start space-x-3">
                      <div class="w-2 h-2 bg-<%= activity[:icon_color] %>-500 rounded-full mt-2 flex-shrink-0"></div>
                      <div class="flex-1 min-w-0">
                        <p class="text-sm text-slate-900 font-medium"><%= activity[:title] %></p>
                        <% if activity[:description].present? %>
                          <p class="text-xs text-slate-600 mt-1"><%= activity[:description] %></p>
                        <% end %>
                        <p class="text-xs text-slate-500 mt-1"><%= activity[:time] %></p>
                      </div>
                    </div>
                  <% end %>
                </div>
              <% else %>
                <div class="text-center py-8">
                  <div class="mx-auto w-12 h-12 bg-gradient-to-br from-slate-100 to-slate-200 rounded-full flex items-center justify-center mb-3">
                    <svg class="h-6 w-6 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                  </div>
                  <p class="text-sm text-slate-500">No recent activity</p>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Enhanced JavaScript for Premium UX -->
<script>
  // Add smooth scroll behavior
  document.documentElement.style.scrollBehavior = 'smooth';

  // Add intersection observer for card animations
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  };

  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.style.opacity = '1';
        entry.target.style.transform = 'translateY(0)';
      }
    });
  }, observerOptions);

  // Observe all cards for animation
  document.querySelectorAll('.group').forEach(card => {
    card.style.opacity = '0';
    card.style.transform = 'translateY(20px)';
    card.style.transition = 'opacity 0.6s ease-out, transform 0.6s ease-out';
    observer.observe(card);
  });
</script>