<% content_for :page_title, "Dashboard" %>

<!-- Upcube-inspired Dashboard Layout -->
<div data-controller="realtime-dashboard" class="min-h-screen bg-gray-50">
  <!-- Page Header -->
  <div class="mb-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">Dashboard</h1>
        <p class="mt-2 text-gray-600">
          Welcome back, <%= current_user.first_name %>! Here's what's happening with your data.
        </p>
      </div>
      <div class="mt-4 sm:mt-0 sm:ml-4">
        <%= link_to new_data_source_path, 
            class: "inline-flex items-center px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-lg hover:bg-indigo-700 transition-colors duration-200" do %>
          <svg class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
          </svg>
          Add Data Source
        <% end %>
      </div>
    </div>
  </div>

  <!-- Status Bar -->
  <div class="mb-8">
    <div class="bg-white rounded-lg border border-gray-200 p-4">
      <div class="flex flex-wrap items-center gap-6">
        <div class="flex items-center gap-2">
          <div class="h-2 w-2 bg-green-500 rounded-full"></div>
          <span class="text-sm font-medium text-gray-700">System Operational</span>
        </div>
        <div class="flex items-center gap-2">
          <svg class="h-4 w-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          <span class="text-sm text-gray-600">Updated <%= Time.current.strftime("%I:%M %p") %></span>
        </div>
        <div class="flex items-center gap-2">
          <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse" data-connection-status></div>
          <span class="text-sm text-gray-600" data-last-updated>Live Data</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Key Metrics Cards -->
  <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 mb-8">
    <!-- Total Data Sources -->
    <div class="bg-white rounded-lg border border-gray-200 p-6">
      <div class="flex items-center justify-between mb-4">
        <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-100">
          <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path>
          </svg>
        </div>
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
          +12%
        </span>
      </div>
      <div>
        <p class="text-sm font-medium text-gray-600 mb-1">Data Sources</p>
        <p class="text-3xl font-bold text-gray-900" data-realtime-dashboard-target="totalDataSources"><%= @stats[:total_data_sources] %></p>
        <p class="text-sm text-gray-500">Total connected sources</p>
      </div>
    </div>

    <!-- Connected Sources -->
    <div class="bg-white rounded-lg border border-gray-200 p-6">
      <div class="flex items-center justify-between mb-4">
        <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-green-100">
          <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
        </div>
        <div class="w-16 h-2 bg-gray-200 rounded-full overflow-hidden">
          <div class="h-full bg-green-500 rounded-full transition-all duration-300" style="width: <%= @stats[:total_data_sources] > 0 ? (@stats[:connected_sources].to_f / @stats[:total_data_sources] * 100).round : 0 %>%"></div>
        </div>
      </div>
      <div>
        <p class="text-sm font-medium text-gray-600 mb-1">Connected</p>
        <p class="text-3xl font-bold text-gray-900" data-realtime-dashboard-target="connectedSources"><%= @stats[:connected_sources] %></p>
        <p class="text-sm text-gray-500">of <%= @stats[:total_data_sources] %> sources active</p>
      </div>
    </div>

    <!-- Total Records -->
    <div class="bg-white rounded-lg border border-gray-200 p-6">
      <div class="flex items-center justify-between mb-4">
        <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-purple-100">
          <svg class="h-6 w-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
          </svg>
        </div>
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
          +8.2%
        </span>
      </div>
      <div>
        <p class="text-sm font-medium text-gray-600 mb-1">Total Records</p>
        <p class="text-3xl font-bold text-gray-900"><%= number_with_delimiter(@stats[:total_records]) %></p>
        <p class="text-sm text-gray-500">Records processed</p>
      </div>
    </div>

    <!-- Last Sync -->
    <div class="bg-white rounded-lg border border-gray-200 p-6">
      <div class="flex items-center justify-between mb-4">
        <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-cyan-100">
          <svg class="h-6 w-6 text-cyan-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= @stats[:last_sync] ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' %>">
          <div class="h-1.5 w-1.5 <%= @stats[:last_sync] ? 'bg-green-500' : 'bg-gray-400' %> rounded-full mr-1"></div>
          <%= @stats[:last_sync] ? 'Active' : 'Inactive' %>
        </span>
      </div>
      <div>
        <p class="text-sm font-medium text-gray-600 mb-1">Last Sync</p>
        <p class="text-3xl font-bold text-gray-900">
          <%= @stats[:last_sync] ? time_ago_in_words(@stats[:last_sync]) : "Never" %>
        </p>
        <p class="text-sm text-gray-500"><%= @stats[:last_sync] ? 'ago' : 'No sync performed' %></p>
      </div>
    </div>
  </div>

  <!-- Data Quality Monitoring Widget -->
  <%= render 'data_quality_widget' %>
  
  <!-- Real-Time AI Analytics Dashboard -->
  <%= render 'real_time_analytics_widget' %>
  
  <!-- Natural Language Query Interface -->
  <%= render 'natural_language_query_widget' %>
  
  <!-- AI Business Intelligence Agent -->
  <%= render 'bi_agent_widget' %>
  
  <!-- AI Data Integration -->
  <%= render 'ai_data_integration_widget' %>
  
  <!-- AI Insights & Presentation Generator Widget -->
  <%= render 'ai_insights_widget' %>
  
  <!-- Interactive Presentations Widget -->
  <%= render 'interactive_presentations_widget' %>

  <!-- E-commerce Analytics Section -->
  <%= render 'ecommerce_analytics' %>

  <!-- Main Content Grid -->
  <div class="grid grid-cols-1 gap-8 xl:grid-cols-12 mb-8">
    <!-- Data Sources Status -->
    <div class="xl:col-span-8">
      <div class="bg-white rounded-lg border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-3">
              <div class="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-100">
                <svg class="h-5 w-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path>
                </svg>
              </div>
              <div>
                <h3 class="text-lg font-semibold text-gray-900">Data Sources</h3>
                <p class="text-sm text-gray-600">Manage your connected integrations</p>
              </div>
            </div>
            <div class="flex items-center gap-3">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                <%= @data_sources.count %> Active
              </span>
              <%= link_to data_sources_path, class: "text-sm font-medium text-indigo-600 hover:text-indigo-500" do %>
                View all →
              <% end %>
            </div>
          </div>
        </div>
        <div class="p-6">
          <% if @data_sources.any? %>
            <div class="space-y-4">
              <% @data_sources.limit(5).each do |data_source| %>
                <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div class="flex items-center gap-4">
                    <div class="h-10 w-10 rounded-lg <%= case data_source.source_type
                      when 'shopify' then 'bg-green-100 text-green-600'
                      when 'stripe' then 'bg-purple-100 text-purple-600'
                      when 'google_analytics' then 'bg-orange-100 text-orange-600'
                      when 'quickbooks' then 'bg-blue-100 text-blue-600'
                      when 'mailchimp' then 'bg-yellow-100 text-yellow-600'
                      else 'bg-gray-100 text-gray-600'
                      end %> flex items-center justify-center text-sm font-semibold">
                      <%= data_source.source_type.first(2).upcase %>
                    </div>
                    <div>
                      <h4 class="text-sm font-medium text-gray-900"><%= data_source.name %></h4>
                      <p class="text-sm text-gray-500"><%= data_source.source_type.humanize %></p>
                    </div>
                  </div>
                  <div class="flex items-center gap-3">
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium <%= case data_source.status
                      when 'connected' then 'bg-green-100 text-green-800'
                      when 'syncing' then 'bg-blue-100 text-blue-800'
                      when 'error' then 'bg-red-100 text-red-800'
                      else 'bg-gray-100 text-gray-800'
                      end %>">
                      <%= data_source.status.humanize %>
                    </span>
                    <%= link_to data_source_path(data_source), class: "text-indigo-600 hover:text-indigo-500" do %>
                      <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                      </svg>
                    <% end %>
                  </div>
                </div>
              <% end %>
            </div>
          <% else %>
            <div class="text-center py-12">
              <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path>
              </svg>
              <h3 class="mt-2 text-sm font-medium text-gray-900">No data sources</h3>
              <p class="mt-1 text-sm text-gray-500">Get started by connecting your first data source.</p>
              <div class="mt-6">
                <%= link_to new_data_source_path, class: "inline-flex items-center px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-lg hover:bg-indigo-700" do %>
                  <svg class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                  </svg>
                  Add Data Source
                <% end %>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Recent Activity -->
    <div class="xl:col-span-4">
      <div class="bg-white rounded-lg border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900">Recent Activity</h3>
        </div>
        <div class="p-6">
          <% if @recent_activity.any? %>
            <div class="space-y-4" data-realtime-dashboard-target="activityContainer">
              <% @recent_activity.each do |activity| %>
                <div class="flex items-start gap-3">
                  <div class="h-2 w-2 bg-<%= activity[:icon_color] %>-500 rounded-full mt-2"></div>
                  <div class="flex-1">
                    <p class="text-sm text-gray-900"><%= activity[:title] %></p>
                    <% if activity[:description].present? %>
                      <p class="text-xs text-gray-600"><%= activity[:description] %></p>
                    <% end %>
                    <p class="text-xs text-gray-500"><%= activity[:time] %> ago</p>
                  </div>
                </div>
              <% end %>
            </div>
          <% else %>
            <div class="text-center py-8">
              <svg class="mx-auto h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <p class="mt-2 text-sm text-gray-500">No recent activity</p>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  // Real-time dashboard updates
  document.addEventListener('DOMContentLoaded', function() {
    const controller = application.getControllerForElementAndIdentifier(
      document.querySelector('[data-controller="realtime-dashboard"]'),
      'realtime-dashboard'
    );
    
    if (controller) {
      controller.connect();
    }
  });
</script>