<% content_for :title, "Analytics Dashboard" %>

<!-- Upcube-inspired Analytics Dashboard -->
<div class="min-h-screen bg-gray-50">
  
  <!-- Page Header -->
  <div class="mb-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">Analytics Dashboard</h1>
        <p class="mt-2 text-gray-600">
          Comprehensive insights into your data operations and business performance
        </p>
      </div>
      
      <!-- Date Range Selector -->
      <div class="mt-4 sm:mt-0 sm:ml-4">
        <%= form_with url: analytics_path, method: :get, local: true, class: "flex items-center gap-2" do |form| %>
          <%= form.select :date_range, 
              options_for_select([
                ['Last 7 days', '7_days'],
                ['Last 30 days', '30_days'],
                ['Last 90 days', '90_days'],
                ['Last year', '1_year']
              ], @date_range),
              {},
              { 
                class: "block w-full py-2 px-3 border border-gray-300 rounded-lg text-gray-900 focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",
                onchange: "this.form.submit();"
              } %>
        <% end %>
      </div>
    </div>
  </div>

  <!-- Key Metrics -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
    <!-- Total Data Sources -->
    <div class="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-lg transition-shadow duration-200">
      <div class="flex items-center justify-between mb-4">
        <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-100">
          <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M20.25 6.375c0 2.278-3.694 4.125-8.25 4.125S3.75 8.653 3.75 6.375m16.5 0c0-2.278-3.694-4.125-8.25-4.125S3.75 4.097 3.75 6.375m16.5 0v11.25c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125V6.375m16.5 0v3.75m-16.5-3.75v3.75m16.5 0v3.75C20.25 18.653 16.556 20.5 12 20.5s-8.25-1.847-8.25-4.125v-3.75m16.5 0c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125" />
          </svg>
        </div>
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          Sources
        </span>
      </div>
      <div class="text-3xl font-bold text-gray-900 mb-1"><%= number_with_delimiter(@total_data_sources) %></div>
      <p class="text-sm font-medium text-gray-600 mb-1">Total Data Sources</p>
      <div class="text-sm text-gray-500">
        <span class="text-green-600 font-medium"><%= @active_data_sources %> active</span>
      </div>
    </div>

    <!-- Success Rate -->
    <div class="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-lg transition-shadow duration-200">
      <div class="flex items-center justify-between mb-4">
        <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-green-100">
          <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
          Success
        </span>
      </div>
      <div class="text-3xl font-bold text-gray-900 mb-1"><%= @success_rate %>%</div>
      <p class="text-sm font-medium text-gray-600 mb-1">Success Rate</p>
      <div class="w-full bg-gray-200 rounded-full h-2">
        <div class="bg-green-500 h-2 rounded-full transition-all duration-500" style="width: <%= @success_rate %>%"></div>
      </div>
    </div>

    <!-- Total Jobs -->
    <div class="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-lg transition-shadow duration-200">
      <div class="flex items-center justify-between mb-4">
        <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-purple-100">
          <svg class="h-6 w-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M13 10V3L4 14h7v7l9-11h-7z" />
          </svg>
        </div>
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
          Jobs
        </span>
      </div>
      <div class="text-3xl font-bold text-gray-900 mb-1"><%= number_with_delimiter(@total_jobs) %></div>
      <p class="text-sm font-medium text-gray-600 mb-1">Sync Jobs</p>
      <div class="flex items-center gap-4 text-xs">
        <span class="text-green-600 font-medium"><%= @successful_jobs %> success</span>
        <span class="text-red-600 font-medium"><%= @failed_jobs %> failed</span>
      </div>
    </div>

    <!-- Records Processed -->
    <div class="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-lg transition-shadow duration-200">
      <div class="flex items-center justify-between mb-4">
        <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-orange-100">
          <svg class="h-6 w-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        </div>
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
          Records
        </span>
      </div>
      <div class="text-3xl font-bold text-gray-900 mb-1"><%= number_with_delimiter(@total_records) %></div>
      <p class="text-sm font-medium text-gray-600 mb-1">Total Records</p>
      <div class="text-sm text-gray-500">
        <%= number_with_delimiter(@processed_records) %> processed
      </div>
    </div>

    <!-- Processing Rate -->
    <div class="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-lg transition-shadow duration-200">
      <div class="flex items-center justify-between mb-4">
        <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-teal-100">
          <svg class="h-6 w-6 text-teal-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-teal-100 text-teal-800">
          Rate
        </span>
      </div>
      <div class="text-3xl font-bold text-gray-900 mb-1"><%= @processing_rate %>%</div>
      <p class="text-sm font-medium text-gray-600 mb-1">Processing Rate</p>
      <div class="w-full bg-gray-200 rounded-full h-2">
        <div class="bg-teal-500 h-2 rounded-full transition-all duration-500" style="width: <%= @processing_rate %>%"></div>
      </div>
    </div>
  </div>

  <!-- Charts and Detailed Analytics -->
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
    <!-- Data Sources by Type -->
    <div class="bg-white rounded-lg border border-gray-200 p-6">
      <div class="flex items-center gap-3 mb-6">
        <div class="flex h-10 w-10 items-center justify-center rounded-lg bg-indigo-100">
          <svg class="h-5 w-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z" />
            <path stroke-linecap="round" stroke-linejoin="round" d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z" />
          </svg>
        </div>
        <h3 class="text-lg font-semibold text-gray-900">Data Sources by Type</h3>
      </div>
      <div class="space-y-3">
        <% @data_sources_by_type.each_with_index do |(type, count), index| %>
          <% colors = ['bg-blue-500', 'bg-green-500', 'bg-purple-500', 'bg-orange-500', 'bg-red-500'] %>
          <% color = colors[index % colors.length] %>
          <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div class="flex items-center gap-3">
              <div class="h-3 w-3 <%= color %> rounded-full"></div>
              <span class="font-medium text-gray-900"><%= type.humanize %></span>
            </div>
            <span class="text-lg font-semibold text-gray-900"><%= count %></span>
          </div>
        <% end %>
      </div>
    </div>

    <!-- Top Performing Data Sources -->
    <div class="bg-white rounded-lg border border-gray-200 p-6">
      <div class="flex items-center gap-3 mb-6">
        <div class="flex h-10 w-10 items-center justify-center rounded-lg bg-green-100">
          <svg class="h-5 w-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
          </svg>
        </div>
        <h3 class="text-lg font-semibold text-gray-900">Top Performing Sources</h3>
      </div>
      <div class="space-y-3">
        <% if @top_data_sources.any? %>
          <% @top_data_sources.each_with_index do |(name, records), index| %>
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div class="flex items-center gap-3">
                <div class="h-6 w-6 bg-green-600 rounded-lg flex items-center justify-center">
                  <span class="text-white font-bold text-xs"><%= index + 1 %></span>
                </div>
                <span class="font-medium text-gray-900"><%= name %></span>
              </div>
              <span class="text-sm font-semibold text-gray-900"><%= number_with_delimiter(records) %></span>
            </div>
          <% end %>
        <% else %>
          <div class="text-center py-8 text-gray-500">
            <p class="text-sm">No data available for the selected period</p>
          </div>
        <% end %>
      </div>
    </div>
  </div>

  <!-- Activity Timeline & Performance Trends -->
  <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
    <!-- Daily Activity Chart -->
    <div class="lg:col-span-2 bg-white rounded-lg border border-gray-200 p-6">
      <div class="flex items-center gap-3 mb-6">
        <div class="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-100">
          <svg class="h-5 w-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
        </div>
        <h3 class="text-lg font-semibold text-gray-900">Daily Activity Trend</h3>
      </div>
      <div class="space-y-3">
        <% if @daily_activity.any? %>
          <% max_activity = @daily_activity.values.max %>
          <% @daily_activity.each do |date, count| %>
            <div class="flex items-center gap-4">
              <span class="text-sm font-medium text-gray-600 w-16"><%= date.strftime('%m/%d') %></span>
              <div class="flex-1 bg-gray-200 rounded-full h-2">
                <div class="bg-blue-500 h-2 rounded-full transition-all duration-500" 
                     style="width: <%= max_activity > 0 ? (count.to_f / max_activity * 100).round(1) : 0 %>%"></div>
              </div>
              <span class="text-sm font-semibold text-gray-900 w-8"><%= count %></span>
            </div>
          <% end %>
        <% else %>
          <div class="text-center py-8 text-gray-500">
            <p class="text-sm">No activity data for the selected period</p>
          </div>
        <% end %>
      </div>
    </div>

    <!-- Data Source Health -->
    <div class="bg-white rounded-lg border border-gray-200 p-6">
      <div class="flex items-center gap-3 mb-6">
        <div class="flex h-10 w-10 items-center justify-center rounded-lg bg-green-100">
          <svg class="h-5 w-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
          </svg>
        </div>
        <h3 class="text-lg font-semibold text-gray-900">System Health</h3>
      </div>
      <div class="space-y-4">
        <div class="p-3 bg-green-50 border border-green-200 rounded-lg">
          <div class="flex items-center justify-between mb-2">
            <span class="text-sm font-medium text-green-800">Active Sources</span>
            <span class="text-lg font-semibold text-green-700"><%= @active_data_sources %></span>
          </div>
          <div class="w-full bg-green-200 rounded-full h-2">
            <div class="bg-green-500 h-2 rounded-full" style="width: <%= @total_data_sources > 0 ? (@active_data_sources.to_f / @total_data_sources * 100).round(1) : 0 %>%"></div>
          </div>
        </div>
        
        <div class="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <div class="flex items-center justify-between mb-2">
            <span class="text-sm font-medium text-yellow-800">Pending</span>
            <span class="text-lg font-semibold text-yellow-700"><%= [@total_data_sources - @active_data_sources - @failed_jobs, 0].max %></span>
          </div>
          <div class="w-full bg-yellow-200 rounded-full h-2">
            <div class="bg-yellow-500 h-2 rounded-full" style="width: <%= @total_data_sources > 0 ? ([@total_data_sources - @active_data_sources - @failed_jobs, 0].max.to_f / @total_data_sources * 100).round(1) : 0 %>%"></div>
          </div>
        </div>
        
        <div class="p-3 bg-red-50 border border-red-200 rounded-lg">
          <div class="flex items-center justify-between mb-2">
            <span class="text-sm font-medium text-red-800">Issues</span>
            <span class="text-lg font-semibold text-red-700"><%= @failed_jobs %></span>
          </div>
          <div class="w-full bg-red-200 rounded-full h-2">
            <div class="bg-red-500 h-2 rounded-full" style="width: <%= @total_data_sources > 0 ? (@failed_jobs.to_f / @total_data_sources * 100).round(1) : 0 %>%"></div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Recent Errors & Alerts -->
  <% if @recent_errors.any? %>
    <div class="bg-white rounded-lg border border-gray-200 p-6">
      <div class="flex items-center justify-between mb-6">
        <div class="flex items-center gap-3">
          <div class="flex h-10 w-10 items-center justify-center rounded-lg bg-red-100">
            <svg class="h-5 w-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
              <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 class="text-lg font-semibold text-gray-900">Recent Issues & Alerts</h3>
        </div>
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
          <%= @recent_errors.count %> Issues
        </span>
      </div>
      <div class="space-y-3">
        <% @recent_errors.each do |job| %>
          <div class="flex items-center justify-between p-3 bg-red-50 border border-red-200 rounded-lg hover:bg-red-100 transition-colors duration-200">
            <div class="flex items-center gap-3">
              <div class="h-6 w-6 bg-red-500 rounded-lg flex items-center justify-center">
                <svg class="h-3 w-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="3">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </div>
              <div>
                <p class="font-medium text-gray-900"><%= job.data_source.name %></p>
                <p class="text-sm text-gray-600"><%= job.error_message.presence || 'Connection failed' %></p>
              </div>
            </div>
            <div class="text-right">
              <span class="text-sm text-gray-500"><%= time_ago_in_words(job.created_at) %> ago</span>
              <div class="mt-1">
                <button class="text-xs text-red-600 hover:text-red-800 font-medium">Retry</button>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    </div>
  <% else %>
    <div class="bg-white rounded-lg border border-gray-200 p-6">
      <div class="text-center py-8">
        <div class="flex h-16 w-16 items-center justify-center rounded-lg bg-green-100 mx-auto mb-4">
          <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <h3 class="text-lg font-semibold text-gray-900 mb-2">All Systems Operational</h3>
        <p class="text-gray-600">No errors or issues detected in the selected time period.</p>
      </div>
    </div>
  <% end %>

  <!-- Advanced E-commerce Analytics -->
  <% if @ecommerce_insights.present? && @ecommerce_insights.any? %>
    <div class="mt-12">
      <div class="mb-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-2">Advanced Business Intelligence</h2>
        <p class="text-gray-600">Deep insights from your e-commerce data</p>
      </div>

      <!-- Revenue & Growth Metrics -->
      <% if @ecommerce_insights[:total_revenue].present? %>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <!-- Total Revenue -->
          <div class="bg-white rounded-lg border border-gray-200 p-6">
            <div class="flex items-center justify-between mb-4">
              <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-green-100">
                <span class="text-2xl">💰</span>
              </div>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Revenue</span>
            </div>
            <div class="text-3xl font-bold text-gray-900 mb-1">$<%= number_with_delimiter(@ecommerce_insights[:total_revenue][:total_revenue], precision: 2) %></div>
            <p class="text-sm font-medium text-gray-600 mb-1">Total Revenue</p>
            <div class="text-sm text-gray-500">
              AOV: $<%= @ecommerce_insights[:total_revenue][:average_order_value] %>
            </div>
          </div>

          <!-- Customer Growth -->
          <% if @ecommerce_insights[:growth_metrics].present? %>
            <div class="bg-white rounded-lg border border-gray-200 p-6">
              <div class="flex items-center justify-between mb-4">
                <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-100">
                  <span class="text-2xl">📈</span>
                </div>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Growth</span>
              </div>
              <div class="text-3xl font-bold text-gray-900 mb-1">
                <% growth = @ecommerce_insights[:growth_metrics][:customer_growth_rate] %>
                <%= growth > 0 ? '+' : '' %><%= growth %>%
              </div>
              <p class="text-sm font-medium text-gray-600 mb-1">Customer Growth</p>
              <div class="text-sm text-gray-500">
                Revenue: <%= @ecommerce_insights[:growth_metrics][:revenue_growth_rate] > 0 ? '+' : '' %><%= @ecommerce_insights[:growth_metrics][:revenue_growth_rate] %>%
              </div>
            </div>
          <% end %>

          <!-- Market Expansion Score -->
          <% if @ecommerce_insights[:growth_opportunities].present? %>
            <div class="bg-white rounded-lg border border-gray-200 p-6">
              <div class="flex items-center justify-between mb-4">
                <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-purple-100">
                  <span class="text-2xl">🌍</span>
                </div>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">Expansion</span>
              </div>
              <div class="text-3xl font-bold text-gray-900 mb-1"><%= @ecommerce_insights[:growth_opportunities][:market_expansion_score] %></div>
              <p class="text-sm font-medium text-gray-600 mb-1">Expansion Score</p>
              <div class="text-sm text-gray-500">
                Markets: <%= @ecommerce_insights[:growth_opportunities][:geographic_opportunities][:top_markets].count %>
              </div>
            </div>
          <% end %>

          <!-- Risk Assessment -->
          <% if @ecommerce_insights[:risk_indicators].present? %>
            <div class="bg-white rounded-lg border border-gray-200 p-6">
              <div class="flex items-center justify-between mb-4">
                <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-orange-100">
                  <span class="text-2xl">⚠️</span>
                </div>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800 uppercase">
                  <%= @ecommerce_insights[:risk_indicators][:risk_score] %>
                </span>
              </div>
              <div class="text-3xl font-bold text-gray-900 mb-1 capitalize"><%= @ecommerce_insights[:risk_indicators][:risk_score] %></div>
              <p class="text-sm font-medium text-gray-600 mb-1">Risk Level</p>
              <div class="text-sm text-gray-500">
                Churn: <%= @ecommerce_insights[:risk_indicators][:customer_churn_risk] %>
              </div>
            </div>
          <% end %>
          </div>
        <% end %>

      <!-- Detailed Analytics Sections -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Top Customer Segments -->
        <% if @ecommerce_insights[:top_performing_segments].present? %>
          <div class="bg-white rounded-lg border border-gray-200 p-6">
            <div class="flex items-center gap-3 mb-6">
              <div class="flex h-10 w-10 items-center justify-center rounded-lg bg-indigo-100">
                <span class="text-lg">👥</span>
              </div>
              <h3 class="text-lg font-semibold text-gray-900">Customer Segments</h3>
            </div>
            <div class="space-y-3">
              <% @ecommerce_insights[:top_performing_segments].each_with_index do |(segment, data), index| %>
                <div class="flex items-center justify-between p-3 bg-indigo-50 rounded-lg">
                  <div class="flex items-center gap-3">
                    <div class="h-6 w-6 bg-indigo-600 rounded-lg flex items-center justify-center">
                      <span class="text-white font-bold text-xs"><%= index + 1 %></span>
                    </div>
                    <div>
                      <span class="font-medium text-gray-900"><%= segment %></span>
                      <div class="text-sm text-gray-600">
                        <%= data[:order_count] %> orders • <%= data[:unique_customers] %> customers
                      </div>
                    </div>
                  </div>
                  <div class="text-right">
                    <span class="text-sm font-semibold text-gray-900">$<%= number_with_delimiter(data[:total_revenue], precision: 0) %></span>
                    <div class="text-xs text-gray-500">$<%= data[:average_order_value] %> AOV</div>
                  </div>
                </div>
              <% end %>
            </div>
          </div>
        <% end %>

        <!-- Growth Opportunities -->
        <% if @ecommerce_insights[:growth_opportunities].present? %>
          <div class="bg-white rounded-lg border border-gray-200 p-6">
            <div class="flex items-center gap-3 mb-6">
              <div class="flex h-10 w-10 items-center justify-center rounded-lg bg-green-100">
                <span class="text-lg">🚀</span>
              </div>
              <h3 class="text-lg font-semibold text-gray-900">Growth Opportunities</h3>
            </div>
            
            <!-- Geographic Opportunities -->
            <% if @ecommerce_insights[:growth_opportunities][:geographic_opportunities].present? %>
              <div class="mb-6">
                <h4 class="font-medium text-gray-800 mb-3">🌍 Top Markets</h4>
                <div class="space-y-2">
                  <% @ecommerce_insights[:growth_opportunities][:geographic_opportunities][:top_markets].each do |country, orders| %>
                    <div class="flex items-center justify-between p-2 bg-green-50 rounded-lg">
                      <span class="font-medium text-gray-900"><%= country %></span>
                      <span class="text-green-600 font-medium text-sm"><%= orders %> orders</span>
                    </div>
                  <% end %>
                </div>
              </div>
            <% end %>

            <!-- Seasonal Insights -->
            <% if @ecommerce_insights[:growth_opportunities][:seasonal_insights].present? %>
              <div class="mb-6">
                <h4 class="font-medium text-gray-800 mb-3">📅 Peak Months</h4>
                <div class="flex flex-wrap gap-2">
                  <% @ecommerce_insights[:growth_opportunities][:seasonal_insights][:peak_months].each do |month| %>
                    <span class="px-2 py-1 bg-green-100 text-green-800 rounded-lg text-xs font-medium">
                      <%= Date::MONTHNAMES[month] %>
                    </span>
                  <% end %>
                </div>
                <div class="mt-3 text-sm text-gray-600">
                  Seasonality Score: <%= @ecommerce_insights[:growth_opportunities][:seasonal_insights][:seasonality_score] %>%
                </div>
              </div>
            <% end %>

            <!-- Cross-sell Opportunities -->
            <% if @ecommerce_insights[:growth_opportunities][:cross_sell_opportunities].present? %>
              <div>
                <h4 class="font-medium text-gray-800 mb-3">🔗 Cross-sell Opportunities</h4>
                <div class="text-sm text-gray-600">
                  <%= @ecommerce_insights[:growth_opportunities][:cross_sell_opportunities].count %> product combinations identified
                </div>
              </div>
            <% end %>
          </div>
        <% end %>
        </div>

      <!-- Business Insights & Recommendations -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Growth Insights -->
        <% if @ecommerce_insights[:growth_metrics]&.dig(:growth_insights).present? %>
          <div class="bg-white rounded-lg border border-gray-200 p-6">
            <div class="flex items-center gap-3 mb-6">
              <div class="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-100">
                <span class="text-lg">💡</span>
              </div>
              <h3 class="text-lg font-semibold text-gray-900">Growth Insights</h3>
            </div>
            <div class="space-y-3">
              <% @ecommerce_insights[:growth_metrics][:growth_insights].each do |insight| %>
                <div class="flex items-start gap-3 p-3 bg-blue-50 rounded-lg">
                  <div class="h-5 w-5 bg-blue-500 rounded-lg flex items-center justify-center mt-0.5">
                    <span class="text-white text-xs">💡</span>
                  </div>
                  <p class="text-gray-800 text-sm leading-relaxed"><%= insight %></p>
                </div>
              <% end %>
            </div>
          </div>
        <% end %>

        <!-- Risk Recommendations -->
        <% if @ecommerce_insights[:risk_indicators]&.dig(:recommendations).present? %>
          <div class="bg-white rounded-lg border border-gray-200 p-6">
            <div class="flex items-center gap-3 mb-6">
              <div class="flex h-10 w-10 items-center justify-center rounded-lg bg-orange-100">
                <span class="text-lg">⚡</span>
              </div>
              <h3 class="text-lg font-semibold text-gray-900">Action Items</h3>
            </div>
            <div class="space-y-3">
              <% @ecommerce_insights[:risk_indicators][:recommendations].each do |recommendation| %>
                <div class="flex items-start gap-3 p-3 bg-orange-50 rounded-lg">
                  <div class="h-5 w-5 bg-orange-500 rounded-lg flex items-center justify-center mt-0.5">
                    <span class="text-white text-xs">⚡</span>
                  </div>
                  <p class="text-gray-800 text-sm leading-relaxed"><%= recommendation %></p>
                </div>
              <% end %>
            </div>
          </div>
        <% end %>
      </div>
      </div>
    <% end %>
  </div>
</div>