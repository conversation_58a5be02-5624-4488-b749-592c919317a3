<% content_for :page_title, "Analytics Dashboard" %>

<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
  <!-- Premium Header with Glassmorphism -->
  <div class="relative bg-white/80 backdrop-blur-xl border-b border-white/20 shadow-xl">
    <div class="absolute inset-0 bg-gradient-to-r from-blue-600/10 to-indigo-600/10"></div>
    <div class="relative px-4 sm:px-6 lg:px-8 py-8">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div class="space-y-2">
          <div class="flex items-center space-x-3">
            <div class="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg">
              <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <h1 class="text-3xl font-bold bg-gradient-to-r from-gray-900 via-blue-900 to-indigo-900 bg-clip-text text-transparent">
              Analytics Dashboard
            </h1>
          </div>
          <p class="text-slate-600 font-medium">Comprehensive insights into your data operations and business performance</p>
        </div>
        
        <!-- Date Range Selector -->
        <div class="mt-4 sm:mt-0 flex items-center space-x-4">
          <div class="flex items-center space-x-2 px-4 py-2 bg-green-100/80 backdrop-blur-sm rounded-full border border-green-200/50">
            <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span class="text-green-700 text-sm font-semibold">Live</span>
          </div>
          <%= form_with url: analytics_path, method: :get, local: true, class: "flex items-center gap-2" do |form| %>
            <%= form.select :date_range, 
                options_for_select([
                  ['Last 7 days', '7_days'],
                  ['Last 30 days', '30_days'],
                  ['Last 90 days', '90_days'],
                  ['Last year', '1_year']
                ], @date_range),
                {},
                { 
                  class: "block w-full py-3 px-4 border border-slate-300/50 rounded-xl text-gray-900 backdrop-blur-sm bg-white/70 shadow-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-300 focus:shadow-xl",
                  onchange: "this.form.submit();"
                } %>
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <!-- Premium Key Metrics -->
  <div class="px-4 sm:px-6 lg:px-8 py-8">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
      <!-- Total Data Sources -->
      <div class="group relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl hover:-translate-y-1 transition-all duration-500">
        <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-indigo-500/5 rounded-2xl"></div>
        <div class="relative">
          <div class="flex items-center justify-between mb-4">
            <div class="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg group-hover:shadow-blue-500/25 transition-all duration-300">
              <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M20.25 6.375c0 2.278-3.694 4.125-8.25 4.125S3.75 8.653 3.75 6.375m16.5 0c0-2.278-3.694-4.125-8.25-4.125S3.75 4.097 3.75 6.375m16.5 0v11.25c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125V6.375m16.5 0v3.75m-16.5-3.75v3.75m16.5 0v3.75C20.25 16.153 16.556 18 12 18s-8.25-1.847-8.25-4.125v-3.75m16.5 0c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125" />
              </svg>
            </div>
          </div>
          <div class="text-3xl font-bold text-slate-900 mb-1"><%= number_with_delimiter(@total_data_sources) %></div>
          <p class="text-sm font-semibold text-slate-600">Total Sources</p>
          <div class="mt-2 flex items-center space-x-2 text-xs text-slate-500">
            <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span><%= @active_data_sources %> active</span>
          </div>
        </div>
      </div>

      <!-- Success Rate -->
      <div class="group relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl hover:-translate-y-1 transition-all duration-500">
        <div class="absolute inset-0 bg-gradient-to-br from-green-500/5 to-emerald-500/5 rounded-2xl"></div>
        <div class="relative">
          <div class="flex items-center justify-between mb-4">
            <div class="p-3 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl shadow-lg group-hover:shadow-green-500/25 transition-all duration-300">
              <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
          <div class="text-3xl font-bold text-slate-900 mb-1"><%= @success_rate %>%</div>
          <p class="text-sm font-semibold text-slate-600">Success Rate</p>
          <div class="mt-2 w-full bg-slate-200 rounded-full h-2">
            <div class="bg-gradient-to-r from-green-500 to-emerald-600 h-2 rounded-full transition-all duration-1000" style="width: <%= @success_rate %>%"></div>
          </div>
        </div>
      </div>

      <!-- Total Jobs -->
      <div class="group relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl hover:-translate-y-1 transition-all duration-500">
        <div class="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-indigo-500/5 rounded-2xl"></div>
        <div class="relative">
          <div class="flex items-center justify-between mb-4">
            <div class="p-3 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl shadow-lg group-hover:shadow-purple-500/25 transition-all duration-300">
              <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
          </div>
          <div class="text-3xl font-bold text-slate-900 mb-1"><%= number_with_delimiter(@total_jobs) %></div>
          <p class="text-sm font-semibold text-slate-600">Sync Jobs</p>
          <div class="mt-2 flex items-center gap-3 text-xs">
            <span class="text-green-600 font-medium"><%= @successful_jobs %> ✓</span>
            <span class="text-red-600 font-medium"><%= @failed_jobs %> ✗</span>
          </div>
        </div>
      </div>

      <!-- Records Processed -->
      <div class="group relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl hover:-translate-y-1 transition-all duration-500">
        <div class="absolute inset-0 bg-gradient-to-br from-orange-500/5 to-red-500/5 rounded-2xl"></div>
        <div class="relative">
          <div class="flex items-center justify-between mb-4">
            <div class="p-3 bg-gradient-to-br from-orange-500 to-red-600 rounded-xl shadow-lg group-hover:shadow-orange-500/25 transition-all duration-300">
              <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
          </div>
          <div class="text-3xl font-bold text-slate-900 mb-1"><%= number_with_delimiter(@total_records) %></div>
          <p class="text-sm font-semibold text-slate-600">Total Records</p>
          <div class="mt-2 text-xs text-slate-500">
            <%= number_with_delimiter(@processed_records) %> processed
          </div>
        </div>
      </div>

      <!-- Processing Rate -->
      <div class="group relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl hover:-translate-y-1 transition-all duration-500">
        <div class="absolute inset-0 bg-gradient-to-br from-teal-500/5 to-cyan-500/5 rounded-2xl"></div>
        <div class="relative">
          <div class="flex items-center justify-between mb-4">
            <div class="p-3 bg-gradient-to-br from-teal-500 to-cyan-600 rounded-xl shadow-lg group-hover:shadow-teal-500/25 transition-all duration-300">
              <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
          <div class="text-3xl font-bold text-slate-900 mb-1"><%= @processing_rate %>%</div>
          <p class="text-sm font-semibold text-slate-600">Processing Rate</p>
          <div class="mt-2 w-full bg-slate-200 rounded-full h-2">
            <div class="bg-gradient-to-r from-teal-500 to-cyan-600 h-2 rounded-full transition-all duration-1000" style="width: <%= @processing_rate %>%"></div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Premium Charts and Detailed Analytics -->
  <div class="px-4 sm:px-6 lg:px-8 pb-8">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- Data Sources by Type -->
      <div class="relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 overflow-hidden hover:shadow-2xl transition-all duration-500">
        <div class="absolute inset-0 bg-gradient-to-br from-indigo-500/5 to-purple-500/5"></div>
        <div class="relative">
          <div class="px-6 py-5 border-b border-slate-200/50 bg-gradient-to-r from-indigo-50/50 to-purple-50/50">
            <div class="flex items-center space-x-3">
              <div class="p-2 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg shadow-lg">
                <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z" />
                  <path stroke-linecap="round" stroke-linejoin="round" d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z" />
                </svg>
              </div>
              <h3 class="text-lg font-bold text-slate-900">Data Sources by Type</h3>
            </div>
          </div>
          <div class="p-6">
            <div class="space-y-3">
              <% @data_sources_by_type.each_with_index do |(type, count), index| %>
                <% colors = ['from-blue-500 to-indigo-600', 'from-green-500 to-emerald-600', 'from-purple-500 to-indigo-600', 'from-orange-500 to-red-600', 'from-pink-500 to-rose-600'] %>
                <% color = colors[index % colors.length] %>
                <div class="group flex items-center justify-between p-4 bg-gradient-to-r from-slate-50/50 to-white/50 rounded-xl border border-slate-200/50 hover:shadow-md transition-all duration-300">
                  <div class="flex items-center gap-3">
                    <div class="w-4 h-4 bg-gradient-to-r <%= color %> rounded-full shadow-lg"></div>
                    <span class="font-semibold text-slate-900"><%= type.humanize %></span>
                  </div>
                  <span class="text-2xl font-bold text-slate-900"><%= count %></span>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      </div>

      <!-- Top Performing Data Sources -->
      <div class="relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 overflow-hidden hover:shadow-2xl transition-all duration-500">
        <div class="absolute inset-0 bg-gradient-to-br from-green-500/5 to-emerald-500/5"></div>
        <div class="relative">
          <div class="px-6 py-5 border-b border-slate-200/50 bg-gradient-to-r from-green-50/50 to-emerald-50/50">
            <div class="flex items-center space-x-3">
              <div class="p-2 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg shadow-lg">
                <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
              </div>
              <h3 class="text-lg font-bold text-slate-900">Top Performing Sources</h3>
            </div>
          </div>
          <div class="p-6">
            <% if @top_data_sources.any? %>
              <div class="space-y-3">
                <% @top_data_sources.each_with_index do |(name, records), index| %>
                  <div class="group flex items-center justify-between p-4 bg-gradient-to-r from-emerald-50/50 to-green-50/50 rounded-xl border border-emerald-200/50 hover:shadow-md transition-all duration-300">
                    <div class="flex items-center gap-3">
                      <div class="p-2 bg-gradient-to-br from-emerald-500 to-green-600 rounded-lg shadow-lg">
                        <span class="text-white font-bold text-sm">#<%= index + 1 %></span>
                      </div>
                      <span class="font-semibold text-slate-900"><%= name %></span>
                    </div>
                    <span class="text-lg font-bold text-slate-900"><%= number_with_delimiter(records) %></span>
                  </div>
                <% end %>
              </div>
            <% else %>
              <div class="text-center py-12">
                <div class="mx-auto w-16 h-16 bg-gradient-to-br from-slate-100 to-slate-200 rounded-full flex items-center justify-center mb-4">
                  <svg class="h-8 w-8 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <p class="text-slate-500 font-medium">No data available for the selected period</p>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>

    <!-- Activity Timeline & Performance Trends -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mt-8">
      <!-- Daily Activity Chart -->
      <div class="lg:col-span-2 relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 overflow-hidden hover:shadow-2xl transition-all duration-500">
        <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-indigo-500/5"></div>
        <div class="relative">
          <div class="px-6 py-5 border-b border-slate-200/50 bg-gradient-to-r from-blue-50/50 to-indigo-50/50">
            <div class="flex items-center space-x-3">
              <div class="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg shadow-lg">
                <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <h3 class="text-lg font-bold text-slate-900">Daily Activity Trend</h3>
            </div>
          </div>
          <div class="p-6">
            <% if @daily_activity.any? %>
              <div class="space-y-3">
                <% max_activity = @daily_activity.values.max %>
                <% @daily_activity.each do |date, count| %>
                  <div class="flex items-center gap-4">
                    <span class="text-sm font-semibold text-slate-600 w-16"><%= date.strftime('%m/%d') %></span>
                    <div class="flex-1 bg-slate-200 rounded-full h-3 shadow-inner">
                      <div class="bg-gradient-to-r from-blue-500 to-indigo-600 h-3 rounded-full transition-all duration-1000 shadow-lg" 
                           style="width: <%= max_activity > 0 ? (count.to_f / max_activity * 100).round(1) : 0 %>%"></div>
                    </div>
                    <span class="text-sm font-bold text-slate-900 w-8"><%= count %></span>
                  </div>
                <% end %>
              </div>
            <% else %>
              <div class="text-center py-12">
                <div class="mx-auto w-16 h-16 bg-gradient-to-br from-slate-100 to-slate-200 rounded-full flex items-center justify-center mb-4">
                  <svg class="h-8 w-8 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <p class="text-slate-500 font-medium">No activity data for the selected period</p>
              </div>
            <% end %>
          </div>
        </div>
      </div>

      <!-- System Health -->
      <div class="relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 overflow-hidden hover:shadow-2xl transition-all duration-500">
        <div class="absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-green-500/5"></div>
        <div class="relative">
          <div class="px-6 py-5 border-b border-slate-200/50 bg-gradient-to-r from-emerald-50/50 to-green-50/50">
            <div class="flex items-center space-x-3">
              <div class="p-2 bg-gradient-to-br from-emerald-500 to-green-600 rounded-lg shadow-lg">
                <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
              </div>
              <h3 class="text-lg font-bold text-slate-900">System Health</h3>
            </div>
          </div>
          <div class="p-6 space-y-4">
            <!-- Active Sources -->
            <div class="p-4 bg-gradient-to-r from-green-50/50 to-emerald-50/50 rounded-xl border border-green-200/50">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm font-semibold text-green-800">Active Sources</span>
                <span class="text-xl font-bold text-green-700"><%= @active_data_sources %></span>
              </div>
              <div class="w-full bg-green-200 rounded-full h-2 shadow-inner">
                <div class="bg-gradient-to-r from-green-500 to-emerald-600 h-2 rounded-full shadow-lg" style="width: <%= @total_data_sources > 0 ? (@active_data_sources.to_f / @total_data_sources * 100).round(1) : 0 %>%"></div>
              </div>
            </div>
            
            <!-- Pending -->
            <div class="p-4 bg-gradient-to-r from-yellow-50/50 to-amber-50/50 rounded-xl border border-yellow-200/50">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm font-semibold text-yellow-800">Pending</span>
                <span class="text-xl font-bold text-yellow-700"><%= [@total_data_sources - @active_data_sources - @failed_jobs, 0].max %></span>
              </div>
              <div class="w-full bg-yellow-200 rounded-full h-2 shadow-inner">
                <div class="bg-gradient-to-r from-yellow-500 to-amber-600 h-2 rounded-full shadow-lg" style="width: <%= @total_data_sources > 0 ? ([@total_data_sources - @active_data_sources - @failed_jobs, 0].max.to_f / @total_data_sources * 100).round(1) : 0 %>%"></div>
              </div>
            </div>
            
            <!-- Issues -->
            <div class="p-4 bg-gradient-to-r from-red-50/50 to-rose-50/50 rounded-xl border border-red-200/50">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm font-semibold text-red-800">Issues</span>
                <span class="text-xl font-bold text-red-700"><%= @failed_jobs %></span>
              </div>
              <div class="w-full bg-red-200 rounded-full h-2 shadow-inner">
                <div class="bg-gradient-to-r from-red-500 to-rose-600 h-2 rounded-full shadow-lg" style="width: <%= @total_data_sources > 0 ? (@failed_jobs.to_f / @total_data_sources * 100).round(1) : 0 %>%"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Recent Errors & Alerts -->
    <% if @recent_errors.any? %>
      <div class="mt-8 relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 overflow-hidden hover:shadow-2xl transition-all duration-500">
        <div class="absolute inset-0 bg-gradient-to-br from-red-500/5 to-rose-500/5"></div>
        <div class="relative">
          <div class="px-6 py-5 border-b border-slate-200/50 bg-gradient-to-r from-red-50/50 to-rose-50/50">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div class="p-2 bg-gradient-to-br from-red-500 to-rose-600 rounded-lg shadow-lg">
                  <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
                <h3 class="text-lg font-bold text-slate-900">Recent Issues & Alerts</h3>
              </div>
              <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-gradient-to-r from-red-100 to-rose-100 text-red-800 border border-red-200/50 shadow-sm">
                <%= @recent_errors.count %> Issues
              </span>
            </div>
          </div>
          <div class="p-6">
            <div class="space-y-3">
              <% @recent_errors.each do |job| %>
                <div class="group flex items-center justify-between p-4 bg-gradient-to-r from-red-50/50 to-rose-50/50 rounded-xl border border-red-200/50 hover:shadow-md transition-all duration-300">
                  <div class="flex items-center gap-3">
                    <div class="p-2 bg-gradient-to-br from-red-500 to-rose-600 rounded-lg shadow-lg">
                      <svg class="h-4 w-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="3">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </div>
                    <div>
                      <p class="font-semibold text-slate-900"><%= job.data_source.name %></p>
                      <p class="text-sm text-slate-600"><%= job.error_message.presence || 'Connection failed' %></p>
                    </div>
                  </div>
                  <div class="text-right">
                    <span class="text-sm text-slate-500"><%= time_ago_in_words(job.created_at) %> ago</span>
                    <div class="mt-1">
                      <button class="text-xs text-red-600 hover:text-red-800 font-semibold transition-colors">Retry</button>
                    </div>
                  </div>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    <% else %>
      <div class="mt-8 relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 overflow-hidden hover:shadow-2xl transition-all duration-500">
        <div class="absolute inset-0 bg-gradient-to-br from-green-500/5 to-emerald-500/5"></div>
        <div class="relative p-8">
          <div class="text-center py-8">
            <div class="mx-auto w-20 h-20 bg-gradient-to-br from-green-100 to-emerald-200 rounded-full flex items-center justify-center mb-4 shadow-xl">
              <svg class="h-10 w-10 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 class="text-xl font-bold text-slate-900 mb-2">All Systems Operational</h3>
            <p class="text-slate-600">No errors or issues detected in the selected time period.</p>
          </div>
        </div>
      </div>
    <% end %>

    <!-- Advanced E-commerce Analytics -->
    <% if @ecommerce_insights.present? && @ecommerce_insights.any? %>
      <div class="mt-12">
        <div class="mb-8">
          <h2 class="text-2xl font-bold bg-gradient-to-r from-gray-900 via-blue-900 to-indigo-900 bg-clip-text text-transparent">Advanced Business Intelligence</h2>
          <p class="text-slate-600 mt-1">Deep insights from your e-commerce data</p>
        </div>

        <!-- Revenue & Growth Metrics -->
        <% if @ecommerce_insights[:total_revenue].present? %>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Total Revenue -->
            <div class="group relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl hover:-translate-y-1 transition-all duration-500">
              <div class="absolute inset-0 bg-gradient-to-br from-green-500/5 to-emerald-500/5 rounded-2xl"></div>
              <div class="relative">
                <div class="flex items-center justify-between mb-4">
                  <div class="p-3 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl shadow-lg group-hover:shadow-green-500/25 transition-all duration-300">
                    <span class="text-2xl">💰</span>
                  </div>
                </div>
                <div class="text-3xl font-bold text-slate-900 mb-1">$<%= number_with_delimiter(@ecommerce_insights[:total_revenue][:total_revenue], precision: 2) %></div>
                <p class="text-sm font-semibold text-slate-600">Total Revenue</p>
                <div class="mt-2 text-xs text-slate-500">
                  AOV: $<%= @ecommerce_insights[:total_revenue][:average_order_value] %>
                </div>
              </div>
            </div>

            <!-- Customer Growth -->
            <% if @ecommerce_insights[:growth_metrics].present? %>
              <div class="group relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl hover:-translate-y-1 transition-all duration-500">
                <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-indigo-500/5 rounded-2xl"></div>
                <div class="relative">
                  <div class="flex items-center justify-between mb-4">
                    <div class="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg group-hover:shadow-blue-500/25 transition-all duration-300">
                      <span class="text-2xl">📈</span>
                    </div>
                  </div>
                  <div class="text-3xl font-bold text-slate-900 mb-1">
                    <% growth = @ecommerce_insights[:growth_metrics][:customer_growth_rate] %>
                    <%= growth > 0 ? '+' : '' %><%= growth %>%
                  </div>
                  <p class="text-sm font-semibold text-slate-600">Customer Growth</p>
                  <div class="mt-2 text-xs text-slate-500">
                    Revenue: <%= @ecommerce_insights[:growth_metrics][:revenue_growth_rate] > 0 ? '+' : '' %><%= @ecommerce_insights[:growth_metrics][:revenue_growth_rate] %>%
                  </div>
                </div>
              </div>
            <% end %>

            <!-- Market Expansion Score -->
            <% if @ecommerce_insights[:growth_opportunities].present? %>
              <div class="group relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl hover:-translate-y-1 transition-all duration-500">
                <div class="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-indigo-500/5 rounded-2xl"></div>
                <div class="relative">
                  <div class="flex items-center justify-between mb-4">
                    <div class="p-3 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl shadow-lg group-hover:shadow-purple-500/25 transition-all duration-300">
                      <span class="text-2xl">🌍</span>
                    </div>
                  </div>
                  <div class="text-3xl font-bold text-slate-900 mb-1"><%= @ecommerce_insights[:growth_opportunities][:market_expansion_score] %></div>
                  <p class="text-sm font-semibold text-slate-600">Expansion Score</p>
                  <div class="mt-2 text-xs text-slate-500">
                    Markets: <%= @ecommerce_insights[:growth_opportunities][:geographic_opportunities][:top_markets].count %>
                  </div>
                </div>
              </div>
            <% end %>

            <!-- Risk Assessment -->
            <% if @ecommerce_insights[:risk_indicators].present? %>
              <div class="group relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl hover:-translate-y-1 transition-all duration-500">
                <div class="absolute inset-0 bg-gradient-to-br from-orange-500/5 to-red-500/5 rounded-2xl"></div>
                <div class="relative">
                  <div class="flex items-center justify-between mb-4">
                    <div class="p-3 bg-gradient-to-br from-orange-500 to-red-600 rounded-xl shadow-lg group-hover:shadow-orange-500/25 transition-all duration-300">
                      <span class="text-2xl">⚠️</span>
                    </div>
                  </div>
                  <div class="text-3xl font-bold text-slate-900 mb-1 capitalize"><%= @ecommerce_insights[:risk_indicators][:risk_score] %></div>
                  <p class="text-sm font-semibold text-slate-600">Risk Level</p>
                  <div class="mt-2 text-xs text-slate-500">
                    Churn: <%= @ecommerce_insights[:risk_indicators][:customer_churn_risk] %>
                  </div>
                </div>
              </div>
            <% end %>
          </div>
        <% end %>

        <!-- Detailed Analytics Sections -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <!-- Top Customer Segments -->
          <% if @ecommerce_insights[:top_performing_segments].present? %>
            <div class="relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 overflow-hidden hover:shadow-2xl transition-all duration-500">
              <div class="absolute inset-0 bg-gradient-to-br from-indigo-500/5 to-purple-500/5"></div>
              <div class="relative">
                <div class="px-6 py-5 border-b border-slate-200/50 bg-gradient-to-r from-indigo-50/50 to-purple-50/50">
                  <div class="flex items-center space-x-3">
                    <div class="p-2 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg shadow-lg">
                      <span class="text-lg">👥</span>
                    </div>
                    <h3 class="text-lg font-bold text-slate-900">Customer Segments</h3>
                  </div>
                </div>
                <div class="p-6 space-y-3">
                  <% @ecommerce_insights[:top_performing_segments].each_with_index do |(segment, data), index| %>
                    <div class="group flex items-center justify-between p-4 bg-gradient-to-r from-indigo-50/50 to-purple-50/50 rounded-xl border border-indigo-200/50 hover:shadow-md transition-all duration-300">
                      <div class="flex items-center gap-3">
                        <div class="p-2 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg shadow-lg">
                          <span class="text-white font-bold text-sm">#<%= index + 1 %></span>
                        </div>
                        <div>
                          <span class="font-semibold text-slate-900"><%= segment %></span>
                          <div class="text-sm text-slate-600">
                            <%= data[:order_count] %> orders • <%= data[:unique_customers] %> customers
                          </div>
                        </div>
                      </div>
                      <div class="text-right">
                        <span class="text-lg font-bold text-slate-900">$<%= number_with_delimiter(data[:total_revenue], precision: 0) %></span>
                        <div class="text-xs text-slate-500">$<%= data[:average_order_value] %> AOV</div>
                      </div>
                    </div>
                  <% end %>
                </div>
              </div>
            </div>
          <% end %>

          <!-- Growth Opportunities -->
          <% if @ecommerce_insights[:growth_opportunities].present? %>
            <div class="relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 overflow-hidden hover:shadow-2xl transition-all duration-500">
              <div class="absolute inset-0 bg-gradient-to-br from-green-500/5 to-emerald-500/5"></div>
              <div class="relative">
                <div class="px-6 py-5 border-b border-slate-200/50 bg-gradient-to-r from-green-50/50 to-emerald-50/50">
                  <div class="flex items-center space-x-3">
                    <div class="p-2 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg shadow-lg">
                      <span class="text-lg">🚀</span>
                    </div>
                    <h3 class="text-lg font-bold text-slate-900">Growth Opportunities</h3>
                  </div>
                </div>
                <div class="p-6">
                  <!-- Geographic Opportunities -->
                  <% if @ecommerce_insights[:growth_opportunities][:geographic_opportunities].present? %>
                    <div class="mb-6">
                      <h4 class="font-semibold text-slate-800 mb-3 flex items-center gap-2">
                        🌍 Top Markets
                      </h4>
                      <div class="space-y-2">
                        <% @ecommerce_insights[:growth_opportunities][:geographic_opportunities][:top_markets].each do |country, orders| %>
                          <div class="flex items-center justify-between p-3 bg-gradient-to-r from-green-50/50 to-emerald-50/50 rounded-xl border border-green-200/50">
                            <span class="font-medium text-slate-900"><%= country %></span>
                            <span class="text-green-600 font-semibold text-sm"><%= orders %> orders</span>
                          </div>
                        <% end %>
                      </div>
                    </div>
                  <% end %>

                  <!-- Seasonal Insights -->
                  <% if @ecommerce_insights[:growth_opportunities][:seasonal_insights].present? %>
                    <div class="mb-6">
                      <h4 class="font-semibold text-slate-800 mb-3 flex items-center gap-2">
                        📅 Peak Months
                      </h4>
                      <div class="flex flex-wrap gap-2">
                        <% @ecommerce_insights[:growth_opportunities][:seasonal_insights][:peak_months].each do |month| %>
                          <span class="px-3 py-1 bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 rounded-lg text-xs font-semibold border border-green-200/50">
                            <%= Date::MONTHNAMES[month] %>
                          </span>
                        <% end %>
                      </div>
                      <div class="mt-3 text-sm text-slate-600">
                        Seasonality Score: <span class="font-semibold"><%= @ecommerce_insights[:growth_opportunities][:seasonal_insights][:seasonality_score] %>%</span>
                      </div>
                    </div>
                  <% end %>

                  <!-- Cross-sell Opportunities -->
                  <% if @ecommerce_insights[:growth_opportunities][:cross_sell_opportunities].present? %>
                    <div>
                      <h4 class="font-semibold text-slate-800 mb-3 flex items-center gap-2">
                        🔗 Cross-sell Opportunities
                      </h4>
                      <div class="p-3 bg-gradient-to-r from-green-50/50 to-emerald-50/50 rounded-xl border border-green-200/50">
                        <span class="text-sm text-slate-600">
                          <span class="font-semibold text-green-700"><%= @ecommerce_insights[:growth_opportunities][:cross_sell_opportunities].count %></span> product combinations identified
                        </span>
                      </div>
                    </div>
                  <% end %>
                </div>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    <% end %>
  </div>

  <!-- Live Update Indicator -->
  <div class="fixed bottom-6 right-6 z-50">
    <div class="flex items-center space-x-2 px-4 py-2 bg-white/90 backdrop-blur-xl rounded-full shadow-xl border border-white/20">
      <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
      <span class="text-xs font-semibold text-slate-700">Live Analytics</span>
    </div>
  </div>
</div>

<!-- Enhanced JavaScript for Premium UX -->
<script>
  // Add smooth scroll behavior
  document.documentElement.style.scrollBehavior = 'smooth';

  // Add intersection observer for card animations
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  };

  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.style.opacity = '1';
        entry.target.style.transform = 'translateY(0)';
      }
    });
  }, observerOptions);

  // Observe all cards for animation
  document.querySelectorAll('.group').forEach(card => {
    card.style.opacity = '0';
    card.style.transform = 'translateY(20px)';
    card.style.transition = 'opacity 0.6s ease-out, transform 0.6s ease-out';
    observer.observe(card);
  });
</script>