<% content_for :title, "DataReflow - Autonomous AI Business Intelligence That Predicts and Prevents" %>

<div data-controller="ai-landing-demo interactive-dashboard live-metrics" data-ai-landing-demo-personalization-value="<%= @personalization.to_json.html_safe %>">

<!-- AI-Powered Hero Section -->
<section class="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 overflow-hidden">
  <!-- Animated Background Elements -->
  <div class="absolute inset-0">
    <div class="absolute top-20 left-10 w-64 h-64 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full mix-blend-multiply filter blur-xl animate-pulse"></div>
    <div class="absolute top-40 right-10 w-72 h-72 bg-gradient-to-r from-purple-400/20 to-pink-400/20 rounded-full mix-blend-multiply filter blur-xl animate-pulse animation-delay-2000"></div>
    <div class="absolute bottom-20 left-1/3 w-80 h-80 bg-gradient-to-r from-green-400/20 to-blue-400/20 rounded-full mix-blend-multiply filter blur-xl animate-pulse animation-delay-4000"></div>
  </div>

  <div class="relative z-10 max-w-7xl mx-auto px-6 lg:px-8">
    <div class="grid lg:grid-cols-2 gap-12 items-center">
      <!-- Content Column -->
      <div class="text-center lg:text-left">
        <!-- AI Badge -->
        <div class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-600/10 to-purple-600/10 border border-blue-600/20 rounded-full text-sm font-medium text-blue-700 mb-8">
          <svg class="h-4 w-4 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
          </svg>
          Powered by Autonomous AI
        </div>

        <!-- Main Headline -->
        <h1 class="text-5xl lg:text-7xl font-bold text-gray-900 mb-6 leading-tight">
          Business Intelligence
          <br>
          <span class="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600">
            That Thinks Ahead
          </span>
        </h1>

        <!-- Dynamic Subheadline Based on Personalization -->
        <p class="text-xl lg:text-2xl text-gray-600 mb-8 leading-relaxed" data-ai-landing-demo-target="dynamicSubheadline">
          <%= case @personalization[:likely_business_type]
              when "ecommerce"
                "AI agent monitors your store 24/7, predicts revenue drops, and alerts you to opportunities before your competitors notice."
              when "saas"
                "Autonomous intelligence that predicts churn, identifies expansion opportunities, and optimizes your entire customer journey."
              when "agency"
                "AI-powered insights that help you deliver better client results and identify new revenue streams across all campaigns."
              else
                "The first truly autonomous business intelligence platform. AI monitors your business, predicts outcomes, and prevents problems before they happen."
              end %>
        </p>

        <!-- Value Proposition Points -->
        <div class="flex flex-wrap gap-6 mb-8">
          <div class="flex items-center text-gray-700">
            <div class="h-2 w-2 bg-green-500 rounded-full mr-3"></div>
            <span class="font-medium">Predicts Issues 2+ Weeks Early</span>
          </div>
          <div class="flex items-center text-gray-700">
            <div class="h-2 w-2 bg-green-500 rounded-full mr-3"></div>
            <span class="font-medium">94.7% Prediction Accuracy</span>
          </div>
          <div class="flex items-center text-gray-700">
            <div class="h-2 w-2 bg-green-500 rounded-full mr-3"></div>
            <span class="font-medium">Setup in 15 Minutes</span>
          </div>
        </div>

        <!-- Dynamic CTA -->
        <div class="flex flex-col sm:flex-row gap-4 items-center lg:items-start">
          <%= link_to new_user_registration_path, 
              class: "inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-300",
              data: { action: "click->ai-landing-demo#trackCTA" } do %>
            <span data-ai-landing-demo-target="primaryCTA"><%= @personalization[:primary_cta] %></span>
            <svg class="ml-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
            </svg>
          <% end %>
          
          <button class="inline-flex items-center px-6 py-4 border-2 border-gray-300 text-gray-700 font-semibold rounded-xl hover:bg-gray-50 transition-all duration-300"
                  data-action="click->ai-landing-demo#showLiveDemo">
            <svg class="mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            Watch AI in Action
          </button>
        </div>

        <!-- Trust Indicators -->
        <div class="mt-8 flex items-center gap-6 text-sm text-gray-500">
          <div class="flex items-center">
            <svg class="h-4 w-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
            </svg>
            No Credit Card Required
          </div>
          <div class="flex items-center">
            <svg class="h-4 w-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
            </svg>
            14-Day Free Trial
          </div>
          <div class="flex items-center">
            <svg class="h-4 w-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
            </svg>
            Enterprise-Grade Security
          </div>
        </div>
      </div>

      <!-- Interactive Demo Column -->
      <div class="relative">
        <!-- Live Metrics Dashboard Preview -->
        <div class="bg-white/90 backdrop-blur-sm rounded-2xl shadow-2xl border border-gray-200/50 p-6 transform hover:scale-105 transition-transform duration-300"
             data-ai-landing-demo-target="liveDemo">
          
          <!-- Demo Header -->
          <div class="flex items-center justify-between mb-6">
            <div class="flex items-center gap-3">
              <div class="h-3 w-3 bg-green-500 rounded-full animate-pulse"></div>
              <span class="text-sm font-medium text-gray-600">Live AI Monitoring</span>
            </div>
            <span class="text-xs text-gray-500" data-live-metrics-target="timestamp">Just now</span>
          </div>

          <!-- Live Metrics Grid -->
          <div class="grid grid-cols-2 gap-4 mb-6">
            <div class="bg-gradient-to-br from-green-50 to-emerald-50 p-4 rounded-xl border border-green-200">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm font-medium text-green-800">Revenue/Hour</span>
                <svg class="h-4 w-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                </svg>
              </div>
              <div class="text-2xl font-bold text-green-900" data-live-metrics-target="revenue">$<%= @demo_data[:real_time_metrics][:revenue_rate] %></div>
              <div class="text-xs text-green-700 flex items-center">
                <svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                </svg>
                +12.3% vs last hour
              </div>
            </div>

            <div class="bg-gradient-to-br from-blue-50 to-indigo-50 p-4 rounded-xl border border-blue-200">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm font-medium text-blue-800">Active Users</span>
                <svg class="h-4 w-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                </svg>
              </div>
              <div class="text-2xl font-bold text-blue-900" data-live-metrics-target="users"><%= @demo_data[:real_time_metrics][:customer_activity] %></div>
              <div class="text-xs text-blue-700 flex items-center">
                <svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                </svg>
                +8.5% trend
              </div>
            </div>
          </div>

          <!-- AI Insights -->
          <div class="bg-gradient-to-r from-purple-50 to-indigo-50 p-4 rounded-xl border border-purple-200">
            <div class="flex items-center gap-2 mb-3">
              <div class="h-2 w-2 bg-purple-500 rounded-full animate-pulse"></div>
              <span class="text-sm font-semibold text-purple-800">AI Insight</span>
              <span class="text-xs bg-purple-200 text-purple-700 px-2 py-1 rounded-full">Live</span>
            </div>
            <p class="text-sm text-purple-700 mb-2" data-ai-landing-demo-target="liveInsight">
              <%= @demo_data[:recent_insights].first[:description] %>
            </p>
            <div class="flex items-center justify-between">
              <span class="text-xs text-purple-600">Confidence: <%= @demo_data[:recent_insights].first[:confidence] %>%</span>
              <span class="text-xs font-medium text-purple-800"><%= @demo_data[:recent_insights].first[:impact] %></span>
            </div>
          </div>
        </div>

        <!-- Floating Elements -->
        <div class="absolute -top-4 -right-4 bg-yellow-400 text-yellow-900 px-3 py-1 rounded-full text-xs font-bold animate-bounce">
          Live Data!
        </div>
        
        <div class="absolute -bottom-4 -left-4 bg-gradient-to-r from-green-400 to-blue-500 text-white px-4 py-2 rounded-full text-sm font-medium shadow-lg">
          AI Working 24/7
        </div>
      </div>
    </div>
  </div>

  <!-- Scroll Indicator -->
  <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
    <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
    </svg>
  </div>
</section>

<!-- AI Stats Showcase -->
<section class="py-16 bg-white">
  <div class="max-w-7xl mx-auto px-6 lg:px-8">
    <div class="text-center mb-12">
      <h2 class="text-lg font-semibold text-gray-900 mb-4">Trusted by businesses processing</h2>
    </div>
    
    <div class="grid grid-cols-2 md:grid-cols-6 gap-8 items-center">
      <div class="text-center">
        <div class="text-3xl font-bold text-blue-600 mb-1"><%= @stats[:ai_insights_generated] %></div>
        <div class="text-sm text-gray-600">AI Insights Generated</div>
      </div>
      <div class="text-center">
        <div class="text-3xl font-bold text-green-600 mb-1"><%= @stats[:anomalies_detected] %></div>
        <div class="text-sm text-gray-600">Anomalies Detected</div>
      </div>
      <div class="text-center">
        <div class="text-3xl font-bold text-purple-600 mb-1"><%= @stats[:businesses_served] %></div>
        <div class="text-sm text-gray-600">Businesses Served</div>
      </div>
      <div class="text-center">
        <div class="text-3xl font-bold text-indigo-600 mb-1"><%= @stats[:predictions_accuracy] %></div>
        <div class="text-sm text-gray-600">Prediction Accuracy</div>
      </div>
      <div class="text-center">
        <div class="text-3xl font-bold text-red-600 mb-1"><%= @stats[:data_processed] %></div>
        <div class="text-sm text-gray-600">Records Processed</div>
      </div>
      <div class="text-center">
        <div class="text-3xl font-bold text-gray-900 mb-1"><%= @stats[:uptime] %></div>
        <div class="text-sm text-gray-600">Uptime SLA</div>
      </div>
    </div>
  </div>
</section>

<!-- AI Features Showcase -->
<section class="py-24 bg-gradient-to-br from-gray-50 to-blue-50">
  <div class="max-w-7xl mx-auto px-6 lg:px-8">
    <div class="text-center mb-16">
      <h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
        Meet Your <span class="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">AI Business Partner</span>
      </h2>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto">
        Unlike traditional BI tools that show you what happened, our AI agents predict what will happen and take action to optimize your business.
      </p>
    </div>

    <div class="grid lg:grid-cols-3 gap-8">
      <% @ai_features.each_with_index do |feature, index| %>
        <div class="group relative bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 p-8"
             data-ai-landing-demo-target="featureCard">
          
          <!-- Feature Icon -->
          <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
            <% case feature[:icon] %>
            <% when "ai-brain" %>
              <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
              </svg>
            <% when "anomaly-detection" %>
              <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
              </svg>
            <% when "data-intelligence" %>
              <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
            <% end %>
          </div>

          <h3 class="text-xl font-bold text-gray-900 mb-4"><%= feature[:title] %></h3>
          <p class="text-gray-600 mb-6 leading-relaxed"><%= feature[:description] %></p>

          <!-- Benefits -->
          <div class="space-y-2 mb-6">
            <% feature[:benefits].each do |benefit| %>
              <div class="flex items-center text-sm text-gray-700">
                <svg class="h-4 w-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
                <%= benefit %>
              </div>
            <% end %>
          </div>

          <!-- Demo Button -->
          <% if feature[:demo_available] %>
            <button class="w-full py-3 px-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-medium rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-300 transform group-hover:scale-105"
                    data-action="click->ai-landing-demo#showFeatureDemo"
                    data-feature="<%= feature[:title].parameterize %>">
              <span class="flex items-center justify-center">
                <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                See Live Demo
              </span>
            </button>
          <% end %>

          <!-- Floating Badge -->
          <div class="absolute -top-3 -right-3 bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-3 py-1 rounded-full text-xs font-bold">
            LIVE
          </div>
        </div>
      <% end %>
    </div>
  </div>
</section>

<!-- Real-Time Analytics Showcase -->
<section class="py-24 bg-white">
  <div class="max-w-7xl mx-auto px-6 lg:px-8">
    <div class="grid lg:grid-cols-2 gap-12 items-center">
      <!-- Content -->
      <div>
        <h2 class="text-4xl font-bold text-gray-900 mb-6">
          Watch Your Business
          <span class="text-transparent bg-clip-text bg-gradient-to-r from-green-600 to-blue-600">
            In Real Time
          </span>
        </h2>
        <p class="text-xl text-gray-600 mb-8">
          Our AI monitors every aspect of your business continuously, detecting anomalies, predicting trends, and alerting you to opportunities the moment they appear.
        </p>

        <div class="space-y-4 mb-8">
          <div class="flex items-start gap-4">
            <div class="h-6 w-6 bg-green-100 rounded-full flex items-center justify-center mt-1">
              <svg class="h-3 w-3 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
            </div>
            <div>
              <h4 class="font-semibold text-gray-900">Predictive Anomaly Detection</h4>
              <p class="text-gray-600">AI learns your business patterns and alerts you to issues 2+ weeks before they impact revenue.</p>
            </div>
          </div>

          <div class="flex items-start gap-4">
            <div class="h-6 w-6 bg-blue-100 rounded-full flex items-center justify-center mt-1">
              <svg class="h-3 w-3 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
            </div>
            <div>
              <h4 class="font-semibold text-gray-900">Smart Threshold Learning</h4>
              <p class="text-gray-600">Dynamic thresholds adapt to your business size, seasonality, and growth patterns automatically.</p>
            </div>
          </div>

          <div class="flex items-start gap-4">
            <div class="h-6 w-6 bg-purple-100 rounded-full flex items-center justify-center mt-1">
              <svg class="h-3 w-3 text-purple-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
            </div>
            <div>
              <h4 class="font-semibold text-gray-900">Actionable Insights</h4>
              <p class="text-gray-600">Get specific recommendations with ROI estimates, not just charts and graphs.</p>
            </div>
          </div>
        </div>

        <button class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-green-600 to-blue-600 text-white font-semibold rounded-lg hover:from-green-700 hover:to-blue-700 transition-all duration-300"
                data-action="click->ai-landing-demo#showRealTimeDemo">
          <svg class="mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
          </svg>
          See Real-Time Analytics
        </button>
      </div>

      <!-- Interactive Dashboard Preview -->
      <div class="relative">
        <!-- Embedded Real-Time Widget -->
        <%= render 'dashboard/real_time_analytics_widget' %>
      </div>
    </div>
  </div>
</section>

<!-- Enhanced Data Intelligence Demo -->
<section class="py-24 bg-gradient-to-br from-purple-50 to-indigo-50">
  <div class="max-w-7xl mx-auto px-6 lg:px-8">
    <div class="text-center mb-16">
      <h2 class="text-4xl font-bold text-gray-900 mb-6">
        <span class="text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-indigo-600">
          Smart Data Processing
        </span>
        <br>That Understands Your Business
      </h2>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto">
        Upload any file and watch our AI instantly understand your business context, identify patterns, and suggest optimizations.
      </p>
    </div>

    <!-- Interactive Data Upload Demo -->
    <div class="max-w-4xl mx-auto">
      <div class="bg-white rounded-2xl shadow-xl p-8 border border-gray-200">
        <div class="text-center mb-8">
          <h3 class="text-xl font-semibold text-gray-900 mb-4">Try Our Enhanced Data Preview</h3>
          <p class="text-gray-600">Upload a sample file or use our demo data to see AI business intelligence in action</p>
        </div>

        <!-- Demo File Upload Area -->
        <div class="border-2 border-dashed border-gray-300 rounded-xl p-8 text-center hover:border-purple-400 transition-colors duration-300"
             data-ai-landing-demo-target="uploadDemo">
          <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 48 48">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"></path>
          </svg>
          <h4 class="text-lg font-semibold text-gray-900 mb-2">Drop your CSV, Excel, or JSON file</h4>
          <p class="text-gray-600 mb-4">Or click to browse files</p>
          
          <div class="flex flex-col sm:flex-row gap-3 justify-center">
            <button class="px-6 py-2 bg-purple-600 text-white font-medium rounded-lg hover:bg-purple-700 transition-colors duration-300"
                    data-action="click->ai-landing-demo#triggerFileUpload">
              Choose File
            </button>
            <button class="px-6 py-2 border border-gray-300 text-gray-700 font-medium rounded-lg hover:bg-gray-50 transition-colors duration-300"
                    data-action="click->ai-landing-demo#useDemoData">
              Use Demo Data
            </button>
          </div>
        </div>

        <!-- Demo Results Preview -->
        <div class="mt-8 hidden" data-ai-landing-demo-target="demoResults">
          <div class="grid md:grid-cols-3 gap-6">
            <!-- Business Context Detection -->
            <div class="bg-gradient-to-br from-green-50 to-emerald-50 p-6 rounded-xl border border-green-200">
              <h4 class="font-semibold text-green-900 mb-3">Business Context Detected</h4>
              <div class="space-y-2">
                <div class="flex items-center text-sm text-green-800">
                  <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                  E-commerce Orders
                </div>
                <div class="flex items-center text-sm text-green-800">
                  <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                  Customer Data
                </div>
                <div class="flex items-center text-sm text-green-800">
                  <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                  Revenue Tracking
                </div>
              </div>
            </div>

            <!-- Data Quality Score -->
            <div class="bg-gradient-to-br from-blue-50 to-indigo-50 p-6 rounded-xl border border-blue-200">
              <h4 class="font-semibold text-blue-900 mb-3">Data Quality Score</h4>
              <div class="text-3xl font-bold text-blue-900 mb-2"><%= @demo_data[:data_quality_preview][:overall_score] %>%</div>
              <div class="text-sm text-blue-700">
                <%= @demo_data[:data_quality_preview][:business_fields_detected] %> business fields detected
              </div>
            </div>

            <!-- ROI Estimation -->
            <div class="bg-gradient-to-br from-purple-50 to-pink-50 p-6 rounded-xl border border-purple-200">
              <h4 class="font-semibold text-purple-900 mb-3">Estimated ROI</h4>
              <div class="text-3xl font-bold text-purple-900 mb-2">$24,500</div>
              <div class="text-sm text-purple-700">
                Monthly value from insights
              </div>
            </div>
          </div>

          <!-- Insights Preview -->
          <div class="mt-6 bg-gray-50 rounded-xl p-6">
            <h4 class="font-semibold text-gray-900 mb-4">AI-Generated Insights</h4>
            <div class="space-y-3">
              <% @demo_data[:data_quality_preview][:quality_insights].each do |insight| %>
                <div class="flex items-center text-sm text-gray-700">
                  <svg class="h-4 w-4 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                  </svg>
                  <%= insight %>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- AI-Enhanced Integrations -->
<section class="py-24 bg-white">
  <div class="max-w-7xl mx-auto px-6 lg:px-8">
    <div class="text-center mb-16">
      <h2 class="text-4xl font-bold text-gray-900 mb-6">
        Connects With Everything,
        <span class="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">
          Understands Everything
        </span>
      </h2>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto">
        Our AI doesn't just connect to your tools—it understands what each data source means for your business and how they work together.
      </p>
    </div>

    <!-- Enhanced Integration Grid -->
    <div class="grid grid-cols-2 md:grid-cols-5 gap-6 mb-12">
      <% @integrations.each do |integration| %>
        <div class="group relative bg-gradient-to-br from-gray-50 to-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 p-6 text-center border border-gray-200 hover:border-purple-300">
          <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300">
            <div class="w-6 h-6 bg-white rounded-md"></div>
          </div>
          <div class="font-semibold text-gray-900 mb-1"><%= integration[:name] %></div>
          <div class="text-xs text-purple-600 font-medium"><%= integration[:ai_feature] %></div>
          
          <!-- AI Enhancement Badge -->
          <div class="absolute -top-2 -right-2 bg-gradient-to-r from-yellow-400 to-orange-500 text-white text-xs px-2 py-1 rounded-full font-bold opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            AI+
          </div>
        </div>
      <% end %>
    </div>

    <!-- Custom Integration CTA -->
    <div class="text-center">
      <div class="inline-block p-8 bg-gradient-to-r from-purple-100 to-blue-100 rounded-2xl border border-purple-200">
        <div class="flex items-center justify-center mb-4">
          <svg class="h-8 w-8 text-purple-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
          </svg>
          <span class="text-xl font-semibold text-gray-900">Need a custom integration?</span>
        </div>
        <p class="text-gray-600 mb-6">Our AI can learn any data source. We build custom connectors with intelligent data mapping at no extra cost.</p>
        <button class="px-8 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold rounded-lg hover:from-purple-700 hover:to-blue-700 transition-all duration-300">
          Request Custom Integration
        </button>
      </div>
    </div>
  </div>
</section>

<!-- Enhanced Testimonials with AI Highlights -->
<section class="py-24 bg-gradient-to-br from-gray-50 to-blue-50">
  <div class="max-w-7xl mx-auto px-6 lg:px-8">
    <div class="text-center mb-16">
      <h2 class="text-4xl font-bold text-gray-900 mb-6">
        Real Results from
        <span class="text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-blue-600">
          Real Businesses
        </span>
      </h2>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto">
        See how our AI transforms businesses like yours with predictive insights and autonomous monitoring
      </p>
    </div>

    <div class="grid lg:grid-cols-3 gap-8">
      <% @testimonials.each_with_index do |testimonial, index| %>
        <div class="group relative bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 p-8">
          <!-- AI Feature Badge -->
          <div class="absolute -top-3 left-6 bg-gradient-to-r from-blue-500 to-purple-600 text-white px-4 py-1 rounded-full text-xs font-semibold">
            <%= testimonial[:ai_feature] %>
          </div>

          <!-- Stars Rating -->
          <div class="flex mb-4 mt-4">
            <% testimonial[:rating].times do %>
              <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2L15.09 8.26L22 9L16 14.74L17.18 21.02L12 18.77L6.82 21.02L8 14.74L2 9L8.91 8.26L12 2Z"/>
              </svg>
            <% end %>
          </div>

          <!-- Highlight Box -->
          <div class="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-3 mb-4">
            <div class="text-sm font-semibold text-green-800"><%= testimonial[:highlight] %></div>
          </div>

          <!-- Quote -->
          <blockquote class="text-gray-700 mb-6 italic text-lg leading-relaxed">
            "<%= testimonial[:quote] %>"
          </blockquote>

          <!-- Author -->
          <div class="flex items-center">
            <div class="w-12 h-12 bg-gradient-to-r from-purple-400 to-blue-400 rounded-full flex items-center justify-center mr-4">
              <span class="text-white font-semibold text-lg"><%= testimonial[:name].split.map(&:first).join %></span>
            </div>
            <div>
              <div class="font-semibold text-gray-900"><%= testimonial[:name] %></div>
              <div class="text-gray-600 text-sm"><%= testimonial[:role] %> at <%= testimonial[:company] %></div>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</section>

<!-- Updated Pricing with AI Value -->
<section id="pricing" class="py-24 bg-white">
  <div class="max-w-7xl mx-auto px-6 lg:px-8">
    <div class="text-center mb-16">
      <h2 class="text-4xl font-bold text-gray-900 mb-6">
        Simple Pricing,
        <span class="text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-blue-600">
          Extraordinary Value
        </span>
      </h2>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto">
        Start with our AI-powered free trial. No credit card required. See ROI in week one.
      </p>
    </div>

    <div class="grid lg:grid-cols-3 gap-8 max-w-5xl mx-auto">
      <!-- Free Trial -->
      <div class="relative bg-white rounded-2xl shadow-lg border-2 border-gray-200 p-8">
        <div class="text-center mb-8">
          <h3 class="text-2xl font-bold text-gray-900 mb-2">AI Trial</h3>
          <div class="text-4xl font-bold text-gray-900 mb-1">$0</div>
          <div class="text-gray-600">14 days, full AI features</div>
        </div>
        <ul class="space-y-4 mb-8">
          <li class="flex items-center">
            <svg class="h-5 w-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM10 17L5 12L6.41 10.59L10 14.17L17.59 6.58L19 8L10 17Z"/>
            </svg>
            AI Business Intelligence Agent
          </li>
          <li class="flex items-center">
            <svg class="h-5 w-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM10 17L5 12L6.41 10.59L10 14.17L17.59 6.58L19 8L10 17Z"/>
            </svg>
            Real-time anomaly detection
          </li>
          <li class="flex items-center">
            <svg class="h-5 w-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM10 17L5 12L6.41 10.59L10 14.17L17.59 6.58L19 8L10 17Z"/>
            </svg>
            Up to 2 data sources
          </li>
          <li class="flex items-center">
            <svg class="h-5 w-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM10 17L5 12L6.41 10.59L10 14.17L17.59 6.58L19 8L10 17Z"/>
            </svg>
            10K records/month
          </li>
        </ul>
        <%= link_to new_user_registration_path, class: "w-full block text-center py-3 px-6 border-2 border-purple-600 text-purple-600 font-semibold rounded-lg hover:bg-purple-50 transition-all duration-300" do %>
          Start AI Trial
        <% end %>
      </div>

      <!-- Starter Plan -->
      <div class="relative bg-gradient-to-r from-purple-600 to-blue-600 rounded-2xl shadow-xl text-white transform scale-105">
        <div class="absolute -top-4 left-1/2 transform -translate-x-1/2 bg-yellow-400 text-gray-900 px-4 py-1 rounded-full text-sm font-bold">
          Most Popular
        </div>
        <div class="p-8">
          <div class="text-center mb-8">
            <h3 class="text-2xl font-bold mb-2">AI Starter</h3>
            <div class="text-4xl font-bold mb-1">$49</div>
            <div class="text-purple-100">per month</div>
          </div>
          <ul class="space-y-4 mb-8">
            <li class="flex items-center">
              <svg class="h-5 w-5 text-green-300 mr-3" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM10 17L5 12L6.41 10.59L10 14.17L17.59 6.58L19 8L10 17Z"/>
              </svg>
              Full AI agent with predictions
            </li>
            <li class="flex items-center">
              <svg class="h-5 w-5 text-green-300 mr-3" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM10 17L5 12L6.41 10.59L10 14.17L17.59 6.58L19 8L10 17Z"/>
              </svg>
              Smart anomaly detection
            </li>
            <li class="flex items-center">
              <svg class="h-5 w-5 text-green-300 mr-3" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM10 17L5 12L6.41 10.59L10 14.17L17.59 6.58L19 8L10 17Z"/>
              </svg>
              Up to 5 data sources
            </li>
            <li class="flex items-center">
              <svg class="h-5 w-5 text-green-300 mr-3" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM10 17L5 12L6.41 10.59L10 14.17L17.59 6.58L19 8L10 17Z"/>
              </svg>
              100K records/month
            </li>
            <li class="flex items-center">
              <svg class="h-5 w-5 text-green-300 mr-3" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM10 17L5 12L6.41 10.59L10 14.17L17.59 6.58L19 8L10 17Z"/>
              </svg>
              AI presentations & reports
            </li>
          </ul>
          <button class="w-full py-3 px-6 bg-white text-purple-600 font-semibold rounded-lg hover:bg-gray-100 transition-all duration-300">
            Start 14-Day Trial
          </button>
        </div>
      </div>

      <!-- Growth Plan -->
      <div class="relative bg-white rounded-2xl shadow-lg border-2 border-gray-200 p-8">
        <div class="text-center mb-8">
          <h3 class="text-2xl font-bold text-gray-900 mb-2">AI Growth</h3>
          <div class="text-4xl font-bold text-gray-900 mb-1">$149</div>
          <div class="text-gray-600">per month</div>
        </div>
        <ul class="space-y-4 mb-8">
          <li class="flex items-center">
            <svg class="h-5 w-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM10 17L5 12L6.41 10.59L10 14.17L17.59 6.58L19 8L10 17Z"/>
            </svg>
            Advanced AI agent with scenarios
          </li>
          <li class="flex items-center">
            <svg class="h-5 w-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM10 17L5 12L6.41 10.59L10 14.17L17.59 6.58L19 8L10 17Z"/>
            </svg>
            Competitive intelligence
          </li>
          <li class="flex items-center">
            <svg class="h-5 w-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM10 17L5 12L6.41 10.59L10 14.17L17.59 6.58L19 8L10 17Z"/>
            </svg>
            Up to 15 data sources
          </li>
          <li class="flex items-center">
            <svg class="h-5 w-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM10 17L5 12L6.41 10.59L10 14.17L7.59 6.58L19 8L10 17Z"/>
            </svg>
            500K records/month
          </li>
          <li class="flex items-center">
            <svg class="h-5 w-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM10 17L5 12L6.41 10.59L10 14.17L17.59 6.58L19 8L10 17Z"/>
            </svg>
            Custom AI integrations
          </li>
        </ul>
        <button class="w-full py-3 px-6 border-2 border-purple-600 text-purple-600 font-semibold rounded-lg hover:bg-purple-50 transition-all duration-300">
          Start 14-Day Trial
        </button>
      </div>
    </div>

    <!-- ROI Guarantee -->
    <div class="text-center mt-12">
      <div class="inline-block bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-6">
        <h4 class="font-bold text-green-900 mb-2">AI ROI Guarantee</h4>
        <p class="text-green-800">Our AI will identify opportunities worth more than your subscription cost in the first 30 days, or we'll refund 100%.</p>
      </div>
    </div>
  </div>
</section>

<!-- Final CTA with AI Focus -->
<section class="py-24 bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-600">
  <div class="max-w-4xl mx-auto px-6 lg:px-8 text-center text-white">
    <h2 class="text-4xl md:text-5xl font-bold mb-6">
      Ready to Put AI to Work
      <br>For Your Business?
    </h2>
    <p class="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
      Join thousands of businesses using autonomous AI to predict problems, identify opportunities, and make better decisions faster.
    </p>
    
    <div class="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
      <%= link_to new_user_registration_path, class: "px-8 py-4 bg-white text-purple-600 font-bold rounded-xl hover:bg-gray-100 transition-colors transform hover:scale-105 duration-300" do %>
        Start AI Trial - Free for 14 Days
      <% end %>
      
      <button class="px-6 py-4 border-2 border-white text-white font-semibold rounded-xl hover:bg-white hover:text-purple-600 transition-all duration-300"
              data-action="click->ai-landing-demo#scheduleDemo">
        Schedule AI Demo
      </button>
    </div>

    <div class="grid grid-cols-1 sm:grid-cols-3 gap-8 text-center">
      <div>
        <div class="text-2xl font-bold mb-1">15 min</div>
        <div class="text-sm opacity-75">Setup time</div>
      </div>
      <div>
        <div class="text-2xl font-bold mb-1">24/7</div>
        <div class="text-sm opacity-75">AI monitoring</div>
      </div>
      <div>
        <div class="text-2xl font-bold mb-1">Week 1</div>
        <div class="text-sm opacity-75">See ROI</div>
      </div>
    </div>
  </div>
</section>

</div>

<!-- Live Demo Modal -->
<div data-ai-landing-demo-target="demoModal" class="hidden fixed inset-0 z-50 overflow-y-auto">
  <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:p-0">
    <div class="fixed inset-0 bg-gray-900 bg-opacity-75 transition-opacity"></div>
    <div class="relative inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-6xl sm:w-full sm:p-6">
      <!-- Modal content will be loaded dynamically -->
      <div data-ai-landing-demo-target="modalContent">
        <!-- Dynamic content based on demo type -->
      </div>
    </div>
  </div>
</div>