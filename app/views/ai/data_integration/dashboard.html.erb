<% content_for :title, "Data Integration Dashboard" %>

<div class="min-h-screen bg-gray-50 py-8">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900">Data Integration Dashboard</h1>
      <p class="mt-2 text-gray-600">Monitor and manage your data sources and integrations</p>
    </div>

    <!-- Integration Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 1.79 4 4 4h8c2.21 0 4-1.79 4-4V7c0-2.21-1.79-4-4-4H8c-2.21 0-4 1.79-4 4z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">Total Sources</dt>
              <dd class="text-lg font-medium text-gray-900"><%= @integration_stats[:total_sources] %></dd>
            </dl>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">Active Sources</dt>
              <dd class="text-lg font-medium text-gray-900"><%= @integration_stats[:active_sources] %></dd>
            </dl>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">Total Records</dt>
              <dd class="text-lg font-medium text-gray-900"><%= number_with_delimiter(@integration_stats[:total_records]) %></dd>
            </dl>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">Data Quality Score</dt>
              <dd class="text-lg font-medium text-gray-900"><%= @integration_stats[:data_quality_score] %>%</dd>
            </dl>
          </div>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- Recent Integrations -->
      <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Recent Integrations</h3>
        </div>
        <div class="px-6 py-4">
          <% if @recent_integrations.any? %>
            <div class="space-y-4">
              <% @recent_integrations.each do |integration| %>
                <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div class="flex items-center space-x-3">
                    <div class="flex-shrink-0">
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        <%= integration[:source_type].humanize %>
                      </span>
                    </div>
                    <div class="min-w-0 flex-1">
                      <p class="text-sm font-medium text-gray-900 truncate"><%= integration[:name] %></p>
                      <p class="text-sm text-gray-500"><%= pluralize(integration[:records_count], 'record') %></p>
                    </div>
                  </div>
                  <div class="flex items-center space-x-2">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= integration[:status] == 'connected' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' %>">
                      <%= integration[:status].humanize %>
                    </span>
                    <% if integration[:last_sync] %>
                      <span class="text-xs text-gray-500">Last sync: <%= time_ago_in_words(Time.parse(integration[:last_sync])) %> ago</span>
                    <% end %>
                  </div>
                </div>
              <% end %>
            </div>
          <% else %>
            <div class="text-center py-8">
              <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H7a1 1 0 00-1 1v1m8 0V4.5a.5.5 0 00-.5-.5h-3a.5.5 0 00-.5.5V5"></path>
              </svg>
              <h3 class="mt-2 text-sm font-medium text-gray-900">No integrations yet</h3>
              <p class="mt-1 text-sm text-gray-500">Get started by adding your first data source.</p>
            </div>
          <% end %>
        </div>
      </div>

      <!-- Optimization Opportunities -->
      <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Optimization Opportunities</h3>
        </div>
        <div class="px-6 py-4">
          <% if @optimization_opportunities.any? %>
            <div class="space-y-4">
              <% @optimization_opportunities.each do |opportunity| %>
                <div class="p-4 border border-gray-200 rounded-lg">
                  <div class="flex items-start">
                    <div class="flex-shrink-0">
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= opportunity[:priority] == 'high' ? 'bg-red-100 text-red-800' : opportunity[:priority] == 'medium' ? 'bg-yellow-100 text-yellow-800' : 'bg-blue-100 text-blue-800' %>">
                        <%= opportunity[:priority].humanize %>
                      </span>
                    </div>
                    <div class="ml-3 flex-1">
                      <h4 class="text-sm font-medium text-gray-900"><%= opportunity[:source] %></h4>
                      <p class="text-sm text-gray-600 mt-1"><%= opportunity[:description] %></p>
                      <p class="text-xs text-gray-500 mt-1">Type: <%= opportunity[:type].humanize %></p>
                    </div>
                  </div>
                </div>
              <% end %>
            </div>
          <% else %>
            <div class="text-center py-8">
              <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <h3 class="mt-2 text-sm font-medium text-gray-900">All optimized!</h3>
              <p class="mt-1 text-sm text-gray-500">Your data integrations are running smoothly.</p>
            </div>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Integration Health Overview -->
    <div class="mt-8 bg-white rounded-lg shadow">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Integration Health</h3>
      </div>
      <div class="px-6 py-4">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="text-center">
            <div class="text-2xl font-bold text-gray-900"><%= @integration_stats[:integration_health] %>%</div>
            <div class="text-sm text-gray-500">Overall Health</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-gray-900">
              <% if @integration_stats[:last_sync] %>
                <%= time_ago_in_words(Time.parse(@integration_stats[:last_sync])) %>
              <% else %>
                Never
              <% end %>
            </div>
            <div class="text-sm text-gray-500">Last Sync</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-gray-900"><%= @integration_stats[:sources_trend] %>%</div>
            <div class="text-sm text-gray-500">Sources Trend</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="mt-8 flex justify-center space-x-4">
      <%= link_to "Add New Data Source", new_data_source_path, class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
      <%= link_to "View All Sources", data_sources_path, class: "inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
    </div>
  </div>
</div>

<script>
  // Auto-refresh dashboard stats every 30 seconds
  setInterval(function() {
    fetch('<%= dashboard_stats_ai_data_integration_index_path %>', {
      headers: {
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      }
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        // Update stats without full page reload
        console.log('Dashboard stats updated:', data.timestamp);
      }
    })
    .catch(error => console.error('Error updating dashboard stats:', error));
  }, 30000);
</script>