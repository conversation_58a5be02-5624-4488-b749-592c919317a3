<% content_for :title, "Business Intelligence Agent Dashboard" %>

<div class="min-h-screen bg-gray-50">
  <!-- Header -->
  <div class="bg-white shadow">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="py-6">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-3xl font-bold text-gray-900">Business Intelligence Agent</h1>
            <p class="mt-1 text-sm text-gray-500">Autonomous insights and analytics for your organization</p>
          </div>
          <div class="flex space-x-3">
            <% if @agent_status[:status] == 'active' %>
              <button id="stop-agent-btn" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 10h6v4H9z"></path>
                </svg>
                Stop Agent
              </button>
            <% else %>
              <button id="start-agent-btn" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9 4h10a1 1 0 001-1V7a1 1 0 00-1-1H6a1 1 0 00-1 1v10a1 1 0 001 1z"></path>
                </svg>
                Start Agent
              </button>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Agent Status Card -->
    <div class="bg-white overflow-hidden shadow rounded-lg mb-8">
      <div class="px-4 py-5 sm:p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <% if @agent_status[:status] == 'active' %>
              <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
              </div>
            <% else %>
              <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                <svg class="w-5 h-5 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
              </div>
            <% end %>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">Agent Status</dt>
              <dd class="text-lg font-medium text-gray-900 capitalize"><%= @agent_status[:status] %></dd>
            </dl>
          </div>
          <div class="ml-5 flex-shrink-0">
            <span class="text-sm text-gray-500">Last updated: <%= @agent_status[:last_updated]&.strftime('%B %d, %Y at %I:%M %p') || 'Never' %></span>
          </div>
        </div>
      </div>
    </div>

    <!-- Stats Grid -->
    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
              <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"></path>
            </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Insights Generated</dt>
                <dd class="text-lg font-medium text-gray-900"><%= @agent_status[:insights_count] || 0 %></dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
            </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Uptime</dt>
                <dd class="text-lg font-medium text-gray-900"><%= @agent_status[:uptime] || '0h' %></dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="w-8 h-8 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
              <path d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"></path>
            </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Weekly Reports</dt>
                <dd class="text-lg font-medium text-gray-900"><%= @weekly_reports.count %></dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="w-8 h-8 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clip-rule="evenodd"></path>
            </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Data Sources</dt>
                <dd class="text-lg font-medium text-gray-900"><%= current_organization.data_sources.count %></dd>
              </dl>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Recent Insights -->
    <div class="bg-white shadow overflow-hidden sm:rounded-md mb-8">
      <div class="px-4 py-5 sm:px-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900">Recent Insights</h3>
        <p class="mt-1 max-w-2xl text-sm text-gray-500">Latest insights generated by the BI agent</p>
      </div>
      <ul class="divide-y divide-gray-200">
        <% if @recent_insights.any? %>
          <% @recent_insights.each do |insight| %>
            <li class="px-4 py-4 sm:px-6">
              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <svg class="w-5 h-5 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                <path d="M11 3a1 1 0 10-2 0v1a1 1 0 102 0V3zM15.657 5.757a1 1 0 00-1.414-1.414l-.707.707a1 1 0 001.414 1.414l.707-.707zM18 10a1 1 0 01-1 1h-1a1 1 0 110-2h1a1 1 0 011 1zM5.05 6.464A1 1 0 106.464 5.05l-.707-.707a1 1 0 00-1.414 1.414l.707.707zM5 10a1 1 0 01-1 1H3a1 1 0 110-2h1a1 1 0 011 1zM8 16v-1h4v1a2 2 0 11-4 0zM12 14c.015-.34.208-.646.477-.859a4 4 0 10-4.954 0c.27.213.462.519.477.859h4z"></path>
              </svg>
                  </div>
                  <div class="ml-4">
                    <p class="text-sm font-medium text-gray-900"><%= insight[:title] %></p>
                    <p class="text-sm text-gray-500"><%= insight[:description] %></p>
                  </div>
                </div>
                <div class="flex items-center text-sm text-gray-500">
                  <%= insight[:created_at]&.strftime('%m/%d/%Y') %>
                </div>
              </div>
            </li>
          <% end %>
        <% else %>
          <li class="px-4 py-4 sm:px-6">
            <div class="text-center">
              <p class="text-sm text-gray-500">No insights generated yet. Start the agent to begin generating insights.</p>
            </div>
          </li>
        <% end %>
      </ul>
    </div>

    <!-- Weekly Reports -->
    <div class="bg-white shadow overflow-hidden sm:rounded-md">
      <div class="px-4 py-5 sm:px-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900">Weekly Reports</h3>
        <p class="mt-1 max-w-2xl text-sm text-gray-500">Automated weekly business intelligence reports</p>
      </div>
      <ul class="divide-y divide-gray-200">
        <% if @weekly_reports.any? %>
          <% @weekly_reports.each do |report| %>
            <li class="px-4 py-4 sm:px-6">
              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <svg class="w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
              </svg>
                  </div>
                  <div class="ml-4">
                    <p class="text-sm font-medium text-gray-900"><%= report[:title] %></p>
                    <p class="text-sm text-gray-500"><%= report[:summary] %></p>
                  </div>
                </div>
                <div class="flex items-center space-x-2">
                  <span class="text-sm text-gray-500"><%= report[:created_at]&.strftime('%m/%d/%Y') %></span>
                  <a href="#" class="text-indigo-600 hover:text-indigo-900 text-sm font-medium">View</a>
                </div>
              </div>
            </li>
          <% end %>
        <% else %>
          <li class="px-4 py-4 sm:px-6">
            <div class="text-center">
              <p class="text-sm text-gray-500">No weekly reports available yet.</p>
            </div>
          </li>
        <% end %>
      </ul>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const startBtn = document.getElementById('start-agent-btn');
    const stopBtn = document.getElementById('stop-agent-btn');

    if (startBtn) {
      startBtn.addEventListener('click', function() {
        fetch('/ai/bi_agent/start_agent', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
          }
        })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            location.reload();
          } else {
            alert('Failed to start agent: ' + data.error);
          }
        })
        .catch(error => {
          console.error('Error:', error);
          alert('Failed to start agent');
        });
      });
    }

    if (stopBtn) {
      stopBtn.addEventListener('click', function() {
        fetch('/ai/bi_agent/stop_agent', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
          }
        })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            location.reload();
          } else {
            alert('Failed to stop agent: ' + data.error);
          }
        })
        .catch(error => {
          console.error('Error:', error);
          alert('Failed to stop agent');
        });
      });
    }
  });
</script>