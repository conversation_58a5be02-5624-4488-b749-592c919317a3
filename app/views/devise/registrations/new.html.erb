<% content_for :title, "Start Your Free Trial - DataReflow" %>

<div class="flex min-h-full">
  <!-- Left side - Enhanced Branding with Animations -->
  <div class="hidden lg:flex lg:flex-1 lg:flex-col lg:justify-center lg:px-8 lg:py-12 bg-gradient-to-br from-purple-600 via-blue-600 to-cyan-500 relative overflow-hidden">
    <!-- Animated Background Elements -->
    <div class="absolute inset-0">
      <div class="absolute top-10 left-10 w-72 h-72 bg-white bg-opacity-10 rounded-full blur-3xl animate-pulse"></div>
      <div class="absolute bottom-10 right-10 w-96 h-96 bg-cyan-300 bg-opacity-20 rounded-full blur-3xl animate-pulse delay-1000"></div>
      <div class="absolute top-1/2 left-1/3 w-64 h-64 bg-purple-300 bg-opacity-15 rounded-full blur-2xl animate-bounce delay-500"></div>
    </div>
    
    <div class="mx-auto w-full max-w-sm relative z-10">
      <!-- Enhanced Logo with Glow Effect -->
      <div class="flex items-center mb-8 group">
        <div class="flex h-14 w-14 items-center justify-center rounded-2xl bg-white bg-opacity-20 backdrop-blur-sm border border-white border-opacity-30 shadow-2xl group-hover:scale-110 transition-transform duration-300">
          <svg class="h-9 w-9 text-white drop-shadow-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
          </svg>
        </div>
        <span class="ml-4 text-3xl font-bold text-white drop-shadow-lg">DataReflow</span>
      </div>
      
      <!-- Enhanced Features with Icons and Animations -->
      <div class="space-y-8">
        <div>
          <h2 class="text-4xl font-bold text-white mb-4 leading-tight">
            Transform Your Business Data Into 
            <span class="text-transparent bg-clip-text bg-gradient-to-r from-yellow-200 to-pink-200">
              Growth
            </span>
          </h2>
          <p class="text-xl text-blue-100 leading-relaxed">
            Join 10,000+ businesses that trust DataReflow to turn their scattered data into competitive advantages.
          </p>
        </div>
        
        <div class="space-y-6">
          <div class="flex items-center text-blue-50 group hover:text-white transition-colors duration-300">
            <div class="flex h-10 w-10 items-center justify-center rounded-lg bg-green-400 bg-opacity-30 mr-4 group-hover:bg-opacity-50 transition-all duration-300">
              <svg class="h-6 w-6 text-green-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
              </svg>
            </div>
            <div>
              <div class="font-semibold">Lightning-Fast Setup</div>
              <div class="text-sm text-blue-200">Connect 15+ tools in minutes, not weeks</div>
            </div>
          </div>
          
          <div class="flex items-center text-blue-50 group hover:text-white transition-colors duration-300">
            <div class="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-400 bg-opacity-30 mr-4 group-hover:bg-opacity-50 transition-all duration-300">
              <svg class="h-6 w-6 text-blue-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
            </div>
            <div>
              <div class="font-semibold">Real-Time Intelligence</div>
              <div class="text-sm text-blue-200">Live dashboards that update every minute</div>
            </div>
          </div>
          
          <div class="flex items-center text-blue-50 group hover:text-white transition-colors duration-300">
            <div class="flex h-10 w-10 items-center justify-center rounded-lg bg-purple-400 bg-opacity-30 mr-4 group-hover:bg-opacity-50 transition-all duration-300">
              <svg class="h-6 w-6 text-purple-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
              </svg>
            </div>
            <div>
              <div class="font-semibold">Enterprise Security</div>
              <div class="text-sm text-blue-200">SOC2 compliant with 256-bit encryption</div>
            </div>
          </div>
        </div>

        <!-- Customer Logos -->
        <div class="pt-8 border-t border-white border-opacity-20">
          <p class="text-sm text-blue-200 mb-4">Trusted by leading companies</p>
          <div class="flex items-center space-x-6 opacity-60">
            <div class="h-8 w-16 bg-white bg-opacity-20 rounded"></div>
            <div class="h-8 w-16 bg-white bg-opacity-20 rounded"></div>
            <div class="h-8 w-16 bg-white bg-opacity-20 rounded"></div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Right side - Enhanced Sign up form -->
  <div class="flex flex-1 flex-col justify-center px-4 py-12 sm:px-6 lg:flex-none lg:px-20 xl:px-24 bg-gray-50">
    <div class="mx-auto w-full max-w-sm lg:w-96">
      <!-- Mobile logo with enhanced styling -->
      <div class="lg:hidden flex items-center justify-center mb-8">
        <div class="flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-r from-purple-600 to-blue-600 shadow-lg">
          <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
          </svg>
        </div>
        <span class="ml-4 text-2xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">DataReflow</span>
      </div>

      <!-- Enhanced Header -->
      <div class="text-center lg:text-left">
        <h2 class="text-4xl font-bold leading-tight tracking-tight text-gray-900 mb-2">
          Start Your Free Trial
        </h2>
        <p class="text-lg text-gray-600 mb-2">
          Transform your business data in minutes
        </p>
        <p class="text-sm leading-6 text-gray-500">
          Already have an account?
          <%= link_to "Sign in here", new_user_session_path, 
              class: "font-semibold text-purple-600 hover:text-purple-500 transition-colors" %>
        </p>
      </div>

      <!-- Enhanced Form -->
      <div class="mt-10">
        <%= form_for(resource, as: resource_name, url: registration_path(resource_name), local: true, class: "space-y-6") do |f| %>
          <%= render "devise/shared/error_messages", resource: resource %>
          
          <!-- Organization Name -->
          <div class="space-y-1">
            <%= label_tag :organization_name, "Organization Name", class: "block text-sm font-medium text-gray-900" %>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                </svg>
              </div>
              <%= text_field_tag :organization_name, params[:organization_name],
                  class: "block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg shadow-sm placeholder:text-gray-400 focus:ring-2 focus:ring-purple-600 focus:border-transparent sm:text-sm transition-all duration-200",
                  placeholder: "Acme Corp" %>
            </div>
          </div>

          <!-- Name Fields -->
          <div class="grid grid-cols-2 gap-4">
            <div class="space-y-1">
              <%= f.label :first_name, class: "block text-sm font-medium text-gray-900" %>
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                  </svg>
                </div>
                <%= f.text_field :first_name,
                    class: "block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg shadow-sm placeholder:text-gray-400 focus:ring-2 focus:ring-purple-600 focus:border-transparent sm:text-sm transition-all duration-200",
                    placeholder: "John" %>
              </div>
            </div>
            
            <div class="space-y-1">
              <%= f.label :last_name, class: "block text-sm font-medium text-gray-900" %>
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                  </svg>
                </div>
                <%= f.text_field :last_name,
                    class: "block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg shadow-sm placeholder:text-gray-400 focus:ring-2 focus:ring-purple-600 focus:border-transparent sm:text-sm transition-all duration-200",
                    placeholder: "Doe" %>
              </div>
            </div>
          </div>

          <!-- Email -->
          <div class="space-y-1">
            <%= f.label :email, class: "block text-sm font-medium text-gray-900" %>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
                </svg>
              </div>
              <%= f.email_field :email, autofocus: true, autocomplete: "email",
                  class: "block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg shadow-sm placeholder:text-gray-400 focus:ring-2 focus:ring-purple-600 focus:border-transparent sm:text-sm transition-all duration-200",
                  placeholder: "<EMAIL>" %>
            </div>
          </div>

          <!-- Password -->
          <div class="space-y-1">
            <%= f.label :password, class: "block text-sm font-medium text-gray-900" do %>
              Password
              <% if @minimum_password_length %>
                <span class="text-xs text-gray-500">(<%= @minimum_password_length %> characters minimum)</span>
              <% end %>
            <% end %>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                </svg>
              </div>
              <%= f.password_field :password, autocomplete: "new-password",
                  class: "block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg shadow-sm placeholder:text-gray-400 focus:ring-2 focus:ring-purple-600 focus:border-transparent sm:text-sm transition-all duration-200",
                  placeholder: "Enter a strong password" %>
            </div>
          </div>

          <!-- Password Confirmation -->
          <div class="space-y-1">
            <%= f.label :password_confirmation, "Confirm Password", class: "block text-sm font-medium text-gray-900" %>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <%= f.password_field :password_confirmation, autocomplete: "new-password",
                  class: "block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg shadow-sm placeholder:text-gray-400 focus:ring-2 focus:ring-purple-600 focus:border-transparent sm:text-sm transition-all duration-200",
                  placeholder: "Confirm your password" %>
            </div>
          </div>

          <!-- Terms Agreement -->
          <div class="flex items-start">
            <div class="flex items-center h-5">
              <input type="checkbox" id="terms" required
                     class="h-4 w-4 rounded border-gray-300 text-purple-600 focus:ring-purple-600">
            </div>
            <div class="ml-3 text-sm">
              <label for="terms" class="text-gray-600">
                I agree to the
                <a href="#" class="font-medium text-purple-600 hover:text-purple-500">Terms of Service</a>
                and
                <a href="#" class="font-medium text-purple-600 hover:text-purple-500">Privacy Policy</a>
              </label>
            </div>
          </div>

          <!-- Enhanced Submit Button -->
          <div>
            <%= f.submit "Start My Free Trial", 
                class: "group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-semibold rounded-lg text-white bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200" %>
          </div>
        <% end %>

        <!-- Enhanced Divider -->
        <div class="mt-8">
          <div class="relative">
            <div class="absolute inset-0 flex items-center" aria-hidden="true">
              <div class="w-full border-t border-gray-200"></div>
            </div>
            <div class="relative flex justify-center text-sm font-medium leading-6">
              <span class="bg-gray-50 px-6 text-gray-900">What you get with your free trial</span>
            </div>
          </div>
        </div>

        <!-- Enhanced Features Grid -->
        <div class="mt-6 grid grid-cols-2 gap-4 text-center">
          <div class="p-4 bg-white rounded-lg shadow-sm border border-gray-100">
            <div class="text-2xl font-bold text-purple-600">14 days</div>
            <div class="text-xs text-gray-600">Free trial</div>
          </div>
          <div class="p-4 bg-white rounded-lg shadow-sm border border-gray-100">
            <div class="text-2xl font-bold text-blue-600">5+</div>
            <div class="text-xs text-gray-600">Integrations</div>
          </div>
          <div class="p-4 bg-white rounded-lg shadow-sm border border-gray-100">
            <div class="text-2xl font-bold text-green-600">24/7</div>
            <div class="text-xs text-gray-600">Support</div>
          </div>
          <div class="p-4 bg-white rounded-lg shadow-sm border border-gray-100">
            <div class="text-2xl font-bold text-cyan-600">∞</div>
            <div class="text-xs text-gray-600">Data sync</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
