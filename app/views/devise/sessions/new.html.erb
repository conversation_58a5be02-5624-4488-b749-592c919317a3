<% content_for :title, "Welcome Back - DataReflow" %>

<div class="flex min-h-full">
  <!-- Left side - Enhanced Branding with Dynamic Animations -->
  <div class="hidden lg:flex lg:flex-1 lg:flex-col lg:justify-center lg:px-8 lg:py-12 bg-gradient-to-br from-indigo-600 via-purple-600 to-blue-600 relative overflow-hidden">
    <!-- Animated Background Elements -->
    <div class="absolute inset-0">
      <div class="absolute top-20 right-10 w-80 h-80 bg-white bg-opacity-10 rounded-full blur-3xl animate-pulse delay-300"></div>
      <div class="absolute bottom-20 left-10 w-72 h-72 bg-blue-300 bg-opacity-20 rounded-full blur-3xl animate-pulse delay-700"></div>
      <div class="absolute top-1/3 left-1/2 w-56 h-56 bg-indigo-300 bg-opacity-15 rounded-full blur-2xl animate-bounce delay-1000"></div>
    </div>
    
    <div class="mx-auto w-full max-w-sm relative z-10">
      <!-- Enhanced Logo with Glow Effect -->
      <div class="flex items-center mb-8 group">
        <div class="flex h-14 w-14 items-center justify-center rounded-2xl bg-white bg-opacity-20 backdrop-blur-sm border border-white border-opacity-30 shadow-2xl group-hover:scale-110 transition-transform duration-300">
          <svg class="h-9 w-9 text-white drop-shadow-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
          </svg>
        </div>
        <span class="ml-4 text-3xl font-bold text-white drop-shadow-lg">DataReflow</span>
      </div>
      
      <!-- Enhanced Content -->
      <div class="space-y-8">
        <div>
          <h2 class="text-4xl font-bold text-white mb-4 leading-tight">
            Welcome Back to Your 
            <span class="text-transparent bg-clip-text bg-gradient-to-r from-yellow-200 to-pink-200">
              Data Command Center
            </span>
          </h2>
          <p class="text-xl text-indigo-100 leading-relaxed">
            Your business insights are waiting. Access your unified dashboard and continue growing with data-driven decisions.
          </p>
        </div>
        
        <div class="space-y-6">
          <div class="flex items-center text-indigo-50 group hover:text-white transition-colors duration-300">
            <div class="flex h-10 w-10 items-center justify-center rounded-lg bg-green-400 bg-opacity-30 mr-4 group-hover:bg-opacity-50 transition-all duration-300">
              <svg class="h-6 w-6 text-green-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div>
              <div class="font-semibold">Instant Access</div>
              <div class="text-sm text-indigo-200">Your data is always up-to-date and ready</div>
            </div>
          </div>
          
          <div class="flex items-center text-indigo-50 group hover:text-white transition-colors duration-300">
            <div class="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-400 bg-opacity-30 mr-4 group-hover:bg-opacity-50 transition-all duration-300">
              <svg class="h-6 w-6 text-blue-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
              </svg>
            </div>
            <div>
              <div class="font-semibold">Smart Analytics</div>
              <div class="text-sm text-indigo-200">AI-powered insights from all your tools</div>
            </div>
          </div>
          
          <div class="flex items-center text-indigo-50 group hover:text-white transition-colors duration-300">
            <div class="flex h-10 w-10 items-center justify-center rounded-lg bg-purple-400 bg-opacity-30 mr-4 group-hover:bg-opacity-50 transition-all duration-300">
              <svg class="h-6 w-6 text-purple-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
              </svg>
            </div>
            <div>
              <div class="font-semibold">Secure & Compliant</div>
              <div class="text-sm text-indigo-200">Your data is protected with enterprise security</div>
            </div>
          </div>
        </div>

        <!-- Enhanced Stats Section -->
        <div class="pt-8 border-t border-white border-opacity-20">
          <p class="text-sm text-indigo-200 mb-4">Live platform statistics</p>
          <div class="grid grid-cols-2 gap-4">
            <div class="text-center">
              <div class="text-2xl font-bold text-white">12.5M+</div>
              <div class="text-xs text-indigo-200">Records Processed</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-white">99.98%</div>
              <div class="text-xs text-indigo-200">Uptime</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Right side - Enhanced Sign in form -->
  <div class="flex flex-1 flex-col justify-center px-4 py-12 sm:px-6 lg:flex-none lg:px-20 xl:px-24 bg-gray-50">
    <div class="mx-auto w-full max-w-sm lg:w-96">
      <!-- Mobile logo with enhanced styling -->
      <div class="lg:hidden flex items-center justify-center mb-8">
        <div class="flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-r from-indigo-600 to-purple-600 shadow-lg">
          <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
          </svg>
        </div>
        <span class="ml-4 text-2xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">DataReflow</span>
      </div>

      <!-- Enhanced Header -->
      <div class="text-center lg:text-left">
        <h2 class="text-4xl font-bold leading-tight tracking-tight text-gray-900 mb-2">
          Welcome Back
        </h2>
        <p class="text-lg text-gray-600 mb-2">
          Continue your data journey
        </p>
        <p class="text-sm leading-6 text-gray-500">
          Don't have an account?
          <%= link_to "Start your free trial", new_user_registration_path, 
              class: "font-semibold text-indigo-600 hover:text-indigo-500 transition-colors" %>
        </p>
      </div>

      <!-- Enhanced Form -->
      <div class="mt-10">
        <%= form_for(resource, as: resource_name, url: session_path(resource_name), local: true, class: "space-y-6") do |f| %>
          <%= render "devise/shared/error_messages", resource: resource %>
          
          <!-- Email -->
          <div class="space-y-1">
            <%= f.label :email, class: "block text-sm font-medium text-gray-900" %>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
                </svg>
              </div>
              <%= f.email_field :email, autofocus: true, autocomplete: "email",
                  class: "block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg shadow-sm placeholder:text-gray-400 focus:ring-2 focus:ring-indigo-600 focus:border-transparent sm:text-sm transition-all duration-200",
                  placeholder: "Enter your email address" %>
            </div>
          </div>

          <!-- Password -->
          <div class="space-y-1">
            <%= f.label :password, class: "block text-sm font-medium text-gray-900" %>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                </svg>
              </div>
              <%= f.password_field :password, autocomplete: "current-password",
                  class: "block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg shadow-sm placeholder:text-gray-400 focus:ring-2 focus:ring-indigo-600 focus:border-transparent sm:text-sm transition-all duration-200",
                  placeholder: "Enter your password" %>
            </div>
          </div>

          <!-- Remember me and Forgot password -->
          <div class="flex items-center justify-between">
            <% if devise_mapping.rememberable? %>
              <div class="flex items-center">
                <%= f.check_box :remember_me, class: "h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600" %>
                <%= f.label :remember_me, class: "ml-3 block text-sm leading-6 text-gray-700" do %>
                  Remember me
                <% end %>
              </div>
            <% end %>

            <div class="text-sm leading-6">
              <%= link_to "Forgot your password?", new_password_path(resource_name), 
                  class: "font-semibold text-indigo-600 hover:text-indigo-500 transition-colors" %>
            </div>
          </div>

          <!-- Enhanced Submit Button -->
          <div>
            <%= f.submit "Access My Dashboard", 
                class: "group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-semibold rounded-lg text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200" %>
          </div>
        <% end %>

        <!-- Enhanced Divider -->
        <div class="mt-8">
          <div class="relative">
            <div class="absolute inset-0 flex items-center" aria-hidden="true">
              <div class="w-full border-t border-gray-200"></div>
            </div>
            <div class="relative flex justify-center text-sm font-medium leading-6">
              <span class="bg-gray-50 px-6 text-gray-900">Join thousands of satisfied customers</span>
            </div>
          </div>
        </div>

        <!-- Enhanced Trust Indicators -->
        <div class="mt-6 grid grid-cols-3 gap-4">
          <div class="p-4 bg-white rounded-lg shadow-sm border border-gray-100 text-center">
            <div class="text-2xl font-bold text-indigo-600">99.9%</div>
            <div class="text-xs text-gray-600">Uptime SLA</div>
          </div>
          <div class="p-4 bg-white rounded-lg shadow-sm border border-gray-100 text-center">
            <div class="text-2xl font-bold text-green-600">SOC2</div>
            <div class="text-xs text-gray-600">Certified</div>
          </div>
          <div class="p-4 bg-white rounded-lg shadow-sm border border-gray-100 text-center">
            <div class="text-2xl font-bold text-blue-600">24/7</div>
            <div class="text-xs text-gray-600">Support</div>
          </div>
        </div>

        <!-- Quick Features Preview -->
        <div class="mt-6 p-4 bg-gradient-to-r from-indigo-50 to-purple-50 rounded-lg border border-indigo-100">
          <div class="text-sm font-medium text-gray-900 mb-2">What's waiting for you:</div>
          <div class="grid grid-cols-2 gap-2 text-xs text-gray-600">
            <div class="flex items-center">
              <div class="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
              Real-time dashboards
            </div>
            <div class="flex items-center">
              <div class="w-2 h-2 bg-blue-400 rounded-full mr-2"></div>
              Smart insights
            </div>
            <div class="flex items-center">
              <div class="w-2 h-2 bg-purple-400 rounded-full mr-2"></div>
              Data integrations
            </div>
            <div class="flex items-center">
              <div class="w-2 h-2 bg-orange-400 rounded-full mr-2"></div>
              Custom reports
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
