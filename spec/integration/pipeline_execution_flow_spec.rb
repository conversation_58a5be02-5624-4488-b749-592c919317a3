require 'rails_helper'

RSpec.describe 'Pipeline Execution Flow', type: :integration do
  let(:organization) { create(:organization) }
  let(:user) { create(:user, organization: organization, role: 'admin') }
  let(:data_source) { create(:data_source, organization: organization) }
  
  before do
    # Mock Solid Queue job processing
    allow(PipelineExecutorJob).to receive(:perform_later)
    allow(TaskExecutorJob).to receive(:perform_later)
  end
  
  describe 'Complete pipeline execution with mixed task types' do
    let(:pipeline) { create(:pipeline_execution, 
      organization: organization,
      data_source: data_source,
      user: user,
      pipeline_name: 'Test ETL Pipeline'
    ) }
    
    let!(:extraction_task) { create(:task,
      pipeline_execution: pipeline,
      name: 'Extract Data',
      task_type: 'extraction',
      execution_mode: 'automated',
      position: 1,
      status: 'ready'
    ) }
    
    let!(:validation_task) { create(:task,
      pipeline_execution: pipeline,
      name: 'Validate Data',
      task_type: 'validation',
      execution_mode: 'manual',
      position: 2,
      status: 'pending',
      depends_on: ['Extract Data']
    ) }
    
    let!(:transformation_task) { create(:task,
      pipeline_execution: pipeline,
      name: 'Transform Data',
      task_type: 'transformation',
      execution_mode: 'approval_required',
      position: 3,
      status: 'pending',
      depends_on: ['Validate Data']
    ) }
    
    let!(:notification_task) { create(:task,
      pipeline_execution: pipeline,
      name: 'Send Notification',
      task_type: 'notification',
      execution_mode: 'automated',
      position: 4,
      status: 'pending',
      depends_on: ['Transform Data']
    ) }
    
    it 'executes automated tasks, waits for manual tasks, and requires approvals' do
      # Start pipeline
      expect(pipeline.status).to eq('queued')
      pipeline.start!
      expect(pipeline.status).to eq('running')
      
      # Execute automated extraction task
      expect(extraction_task.can_execute?).to be true
      extraction_task.execute!
      expect(extraction_task.status).to eq('in_progress')
      
      # Simulate task completion
      extraction_task.complete!(records_processed: 100)
      expect(extraction_task.status).to eq('completed')
      
      # Check dependency resolution
      validation_task.check_and_update_readiness
      expect(validation_task.reload.status).to eq('ready')
      
      # Manual task requires assignment
      expect(validation_task.can_execute?).to be false
      validation_task.assignee = user
      expect(validation_task.can_execute?).to be true
      
      # Execute manual task
      validation_task.execute!(user)
      expect(validation_task.status).to eq('in_progress')
      validation_task.complete!(validation_passed: true)
      
      # Transformation task requires approval
      transformation_task.check_and_update_readiness
      expect(transformation_task.reload.status).to eq('ready')
      transformation_task.request_approval!
      expect(transformation_task.status).to eq('waiting_approval')
      
      # Approve and execute
      expect(transformation_task.approve!(user)).to be true
      expect(transformation_task.status).to eq('ready')
      transformation_task.execute!(user)
      transformation_task.complete!(records_transformed: 95)
      
      # Final notification task
      notification_task.check_and_update_readiness
      expect(notification_task.reload.status).to eq('ready')
      notification_task.execute!
      notification_task.complete!(emails_sent: 5)
      
      # Update pipeline status
      pipeline.update_task_progress!
      expect(pipeline.reload.status).to eq('completed')
      expect(pipeline.progress_percentage).to eq(100)
      expect(pipeline.completed_tasks).to eq(4)
      expect(pipeline.failed_tasks).to eq(0)
    end
    
    it 'handles task failures and retries' do
      pipeline.start!
      
      # Simulate extraction failure
      extraction_task.execute!
      extraction_task.fail!('Connection timeout')
      expect(extraction_task.status).to eq('ready') # Auto-retry
      expect(extraction_task.retry_count).to eq(1)
      
      # Retry and succeed
      extraction_task.execute!
      extraction_task.complete!
      
      # Check pipeline is still running
      pipeline.update_task_progress!
      expect(pipeline.reload.status).to eq('running')
    end
    
    it 'supports pipeline pause and resume' do
      pipeline.start!
      extraction_task.execute!
      
      # Pause pipeline
      expect(pipeline.can_pause?).to be true
      pipeline.pause!
      expect(pipeline.status).to eq('paused')
      
      # Tasks should not execute when paused
      extraction_task.complete!
      validation_task.check_and_update_readiness
      expect(validation_task.reload.status).to eq('ready')
      
      # Resume pipeline
      expect(pipeline.can_resume?).to be true
      pipeline.resume!
      expect(pipeline.status).to eq('running')
    end
    
    it 'handles task cancellation' do
      pipeline.start!
      
      # Cancel a ready task
      expect(extraction_task.can_cancel?).to be true
      extraction_task.cancel!
      expect(extraction_task.status).to eq('cancelled')
      
      # Dependent tasks should not become ready
      validation_task.check_and_update_readiness
      expect(validation_task.reload.status).to eq('pending')
      
      # Pipeline should reflect the cancellation
      pipeline.update_task_progress!
      expect(pipeline.reload.status).to eq('failed')
    end
  end
  
  describe 'Pipeline execution with task templates' do
    let(:template) { create(:task_template,
      organization: organization,
      name: 'Standard Validation',
      task_type: 'validation',
      execution_mode: 'automated',
      template_config: { rules: ['required_fields', 'data_types'] }
    ) }
    
    it 'creates tasks from templates' do
      pipeline = create(:pipeline_execution, organization: organization)
      
      # Create task from template
      task = template.create_task_from_template(pipeline, 
        name: 'Validate Customer Data',
        configuration: { additional_rule: 'email_format' }
      )
      
      expect(task).to be_persisted
      expect(task.task_type).to eq('validation')
      expect(task.execution_mode).to eq('automated')
      expect(task.configuration['rules']).to eq(['required_fields', 'data_types'])
      expect(task.configuration['additional_rule']).to eq('email_format')
      expect(task.metadata['template_id']).to eq(template.id)
    end
  end
  
  describe 'Real-time updates via ActionCable' do
    it 'broadcasts task status changes' do
      pipeline = create(:pipeline_execution, organization: organization)
      task = create(:task, pipeline_execution: pipeline, status: 'ready')
      
      # Expect broadcast when task status changes
      expect(ActionCable.server).to receive(:broadcast).with(
        "pipeline_#{pipeline.id}",
        hash_including(type: 'task_status_update', task_id: task.id)
      )
      
      task.execute!
    end
    
    it 'broadcasts to manual task queue' do
      task = create(:task, 
        pipeline_execution: create(:pipeline_execution, organization: organization),
        execution_mode: 'manual',
        status: 'pending'
      )
      
      # Expect broadcast when manual task becomes ready
      expect(ActionCable.server).to receive(:broadcast).with(
        "manual_task_queue",
        hash_including(type: 'new_manual_task')
      )
      
      task.update!(status: 'ready')
    end
  end
  
  describe 'Pipeline metrics and monitoring' do
    let(:pipeline) { create(:pipeline_execution, 
      organization: organization,
      started_at: 2.hours.ago
    ) }
    
    before do
      create_list(:task, 3, 
        pipeline_execution: pipeline, 
        status: 'completed',
        started_at: 1.hour.ago,
        completed_at: 30.minutes.ago
      )
      
      create(:task, 
        pipeline_execution: pipeline, 
        status: 'failed',
        error_message: 'Validation error'
      )
    end
    
    it 'calculates pipeline metrics correctly' do
      expect(pipeline.total_tasks).to eq(4)
      expect(pipeline.completed_tasks).to eq(3)
      expect(pipeline.failed_tasks).to eq(1)
      expect(pipeline.progress_percentage).to eq(75)
      expect(pipeline.duration_seconds).to be > 0
    end
    
    it 'tracks execution history' do
      pipeline.complete!
      
      # Create another execution
      new_pipeline = pipeline.retry!
      expect(new_pipeline.retry_count).to eq(1)
      expect(new_pipeline.metadata['original_pipeline_id']).to eq(pipeline.id)
    end
  end
end